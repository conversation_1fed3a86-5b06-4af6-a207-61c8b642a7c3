/**
 * 自定义节点组件
 * 用于在React Flow中渲染视觉脚本节点
 */

import React, { memo, useCallback } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, Tag, Tooltip, Button } from 'antd';
import { DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { IVisualScriptNode, DataType } from '../core/types';
import './CustomNodeComponent.css';

interface CustomNodeData {
  scriptNode: IVisualScriptNode;
  onSelect?: () => void;
  onDelete?: () => void;
  readonly?: boolean;
}

/**
 * 获取数据类型对应的颜色
 */
function getDataTypeColor(dataType: DataType): string {
  const colorMap: Record<DataType, string> = {
    [DataType.TRIGGER]: '#ff4d4f',
    [DataType.BOOLEAN]: '#52c41a',
    [DataType.NUMBER]: '#1890ff',
    [DataType.STRING]: '#fa8c16',
    [DataType.OBJECT]: '#722ed1',
    [DataType.ARRAY]: '#eb2f96',
    [DataType.VECTOR2]: '#13c2c2',
    [DataType.VECTOR3]: '#13c2c2',
    [DataType.VECTOR4]: '#13c2c2',
    [DataType.QUATERNION]: '#13c2c2',
    [DataType.MATRIX3]: '#13c2c2',
    [DataType.MATRIX4]: '#13c2c2',
    [DataType.COLOR]: '#fa541c',
    [DataType.ENTITY]: '#a0d911',
    [DataType.COMPONENT]: '#a0d911',
    [DataType.MATERIAL]: '#d4b106',
    [DataType.TEXTURE]: '#d4b106',
    [DataType.MESH]: '#d4b106',
    [DataType.ANIMATION]: '#f759ab',
    [DataType.AUDIO]: '#40a9ff',
    [DataType.FUNCTION]: '#9254de',
    [DataType.EVENT]: '#ff7a45',
    [DataType.PROMISE]: '#36cfc9',
    [DataType.STREAM]: '#36cfc9',
    [DataType.ANY]: '#8c8c8c'
  };
  
  return colorMap[dataType] || '#8c8c8c';
}

/**
 * 获取数据类型显示名称
 */
function getDataTypeLabel(dataType: DataType): string {
  const labelMap: Record<DataType, string> = {
    [DataType.TRIGGER]: 'T',
    [DataType.BOOLEAN]: 'B',
    [DataType.NUMBER]: 'N',
    [DataType.STRING]: 'S',
    [DataType.OBJECT]: 'O',
    [DataType.ARRAY]: 'A',
    [DataType.VECTOR2]: 'V2',
    [DataType.VECTOR3]: 'V3',
    [DataType.VECTOR4]: 'V4',
    [DataType.QUATERNION]: 'Q',
    [DataType.MATRIX3]: 'M3',
    [DataType.MATRIX4]: 'M4',
    [DataType.COLOR]: 'C',
    [DataType.ENTITY]: 'E',
    [DataType.COMPONENT]: 'CP',
    [DataType.MATERIAL]: 'MT',
    [DataType.TEXTURE]: 'TX',
    [DataType.MESH]: 'MS',
    [DataType.ANIMATION]: 'AN',
    [DataType.AUDIO]: 'AU',
    [DataType.FUNCTION]: 'F',
    [DataType.EVENT]: 'EV',
    [DataType.PROMISE]: 'P',
    [DataType.STREAM]: 'ST',
    [DataType.ANY]: '*'
  };
  
  return labelMap[dataType] || '?';
}

/**
 * 自定义节点组件
 */
export const CustomNodeComponent: React.FC<NodeProps<CustomNodeData>> = memo(({ 
  data, 
  selected 
}) => {
  const { scriptNode, onSelect, onDelete, readonly } = data;
  
  const inputPorts = scriptNode.getInputPorts();
  const outputPorts = scriptNode.getOutputPorts();

  // 处理节点点击
  const handleNodeClick = useCallback(() => {
    onSelect?.();
  }, [onSelect]);

  // 处理删除按钮点击
  const handleDeleteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.();
  }, [onDelete]);

  // 计算节点颜色
  const nodeColor = scriptNode.enabled ? '#ffffff' : '#f5f5f5';
  const borderColor = selected ? '#1890ff' : '#d9d9d9';

  return (
    <div className={`custom-node ${selected ? 'selected' : ''} ${!scriptNode.enabled ? 'disabled' : ''}`}>
      {/* 输入端口 */}
      {inputPorts.map((port, index) => (
        <Handle
          key={`input-${port.name}`}
          type="target"
          position={Position.Left}
          id={port.name}
          style={{
            top: 40 + index * 25,
            background: getDataTypeColor(port.type),
            width: 12,
            height: 12,
            border: '2px solid white'
          }}
        >
          <Tooltip title={`${port.label} (${port.type})`} placement="left">
            <div className="port-label input-label">
              {port.label}
            </div>
          </Tooltip>
        </Handle>
      ))}

      {/* 输出端口 */}
      {outputPorts.map((port, index) => (
        <Handle
          key={`output-${port.name}`}
          type="source"
          position={Position.Right}
          id={port.name}
          style={{
            top: 40 + index * 25,
            background: getDataTypeColor(port.type),
            width: 12,
            height: 12,
            border: '2px solid white'
          }}
        >
          <Tooltip title={`${port.label} (${port.type})`} placement="right">
            <div className="port-label output-label">
              {port.label}
            </div>
          </Tooltip>
        </Handle>
      ))}

      {/* 节点主体 */}
      <Card
        size="small"
        className="node-card"
        style={{ 
          backgroundColor: nodeColor,
          borderColor: borderColor,
          borderWidth: selected ? 2 : 1
        }}
        onClick={handleNodeClick}
        bodyStyle={{ 
          padding: '8px 12px',
          minWidth: 150,
          minHeight: Math.max(60, Math.max(inputPorts.length, outputPorts.length) * 25 + 40)
        }}
      >
        {/* 节点头部 */}
        <div className="node-header">
          <div className="node-icon-name">
            <span className="node-icon">
              {scriptNode.type.includes('event') ? '⚡' : 
               scriptNode.type.includes('math') ? '🔢' : 
               scriptNode.type.includes('logic') ? '🧠' : 
               scriptNode.type.includes('flow') ? '🔄' : 
               scriptNode.type.includes('data') ? '📊' : '📦'}
            </span>
            <span className="node-name">{scriptNode.name}</span>
          </div>
          
          {!readonly && (
            <div className="node-actions">
              <Tooltip title="节点设置">
                <Button
                  type="text"
                  size="small"
                  icon={<SettingOutlined />}
                  className="action-button"
                />
              </Tooltip>
              <Tooltip title="删除节点">
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  className="action-button delete-button"
                  onClick={handleDeleteClick}
                />
              </Tooltip>
            </div>
          )}
        </div>

        {/* 节点类型标签 */}
        <div className="node-type">
          <Tag size="small" color="blue">
            {scriptNode.category}
          </Tag>
        </div>

        {/* 端口信息 */}
        <div className="ports-info">
          <div className="input-ports">
            {inputPorts.map((port) => (
              <div key={port.name} className="port-info">
                <span 
                  className="port-type-indicator"
                  style={{ backgroundColor: getDataTypeColor(port.type) }}
                >
                  {getDataTypeLabel(port.type)}
                </span>
                <span className="port-name">{port.label}</span>
              </div>
            ))}
          </div>
          
          <div className="output-ports">
            {outputPorts.map((port) => (
              <div key={port.name} className="port-info output">
                <span className="port-name">{port.label}</span>
                <span 
                  className="port-type-indicator"
                  style={{ backgroundColor: getDataTypeColor(port.type) }}
                >
                  {getDataTypeLabel(port.type)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* 节点状态指示器 */}
        {!scriptNode.enabled && (
          <div className="node-status disabled">
            <Tag color="red" size="small">已禁用</Tag>
          </div>
        )}
      </Card>
    </div>
  );
});

CustomNodeComponent.displayName = 'CustomNodeComponent';

export default CustomNodeComponent;
