/**
 * 自定义节点组件样式
 */

.custom-node {
  position: relative;
  min-width: 150px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.custom-node.selected .node-card {
  border-color: #1890ff;
  border-width: 2px;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-node.disabled {
  opacity: 0.6;
}

/* 节点卡片样式 */
.node-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  background: white;
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.node-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.node-card .ant-card-body {
  padding: 8px 12px;
}

/* 节点头部样式 */
.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.node-icon-name {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-name {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.node-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.custom-node:hover .node-actions {
  opacity: 1;
}

.action-button {
  width: 20px;
  height: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.action-button:hover {
  background: #f5f5f5;
}

.delete-button:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 节点类型标签 */
.node-type {
  margin-bottom: 8px;
}

.node-type .ant-tag {
  margin: 0;
  font-size: 10px;
  padding: 0 4px;
  height: 16px;
  line-height: 14px;
}

/* 端口信息样式 */
.ports-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 20px;
}

.input-ports,
.output-ports {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.port-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #666;
  height: 18px;
}

.port-info.output {
  justify-content: flex-end;
}

.port-type-indicator {
  width: 16px;
  height: 12px;
  border-radius: 2px;
  color: white;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.port-name {
  font-size: 10px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

/* 端口标签样式 */
.port-label {
  position: absolute;
  font-size: 10px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 3px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-label {
  right: 20px;
  transform: translateY(-50%);
}

.output-label {
  left: 20px;
  transform: translateY(-50%);
}

/* 节点状态样式 */
.node-status {
  margin-top: 6px;
  text-align: center;
}

.node-status.disabled .ant-tag {
  margin: 0;
}

/* 执行状态指示器 */
.custom-node.executing {
  animation: nodeExecuting 1s ease-in-out infinite;
}

@keyframes nodeExecuting {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.custom-node.executing .node-card {
  border-color: #52c41a;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.3);
}

/* 错误状态 */
.custom-node.error .node-card {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.custom-node.error .node-name {
  color: #ff4d4f;
}

/* 警告状态 */
.custom-node.warning .node-card {
  border-color: #faad14;
  background: #fffbe6;
}

/* 成功状态 */
.custom-node.success .node-card {
  border-color: #52c41a;
  background: #f6ffed;
}

/* 端口连接状态 */
.react-flow__handle.connected {
  background: #52c41a;
  border-color: #52c41a;
}

.react-flow__handle.connecting {
  background: #1890ff;
  border-color: #1890ff;
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* 拖拽状态 */
.custom-node.dragging {
  opacity: 0.8;
  transform: rotate(2deg);
}

.custom-node.dragging .node-card {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-node {
    min-width: 120px;
  }
  
  .node-name {
    font-size: 12px;
    max-width: 80px;
  }
  
  .port-label {
    font-size: 9px;
    padding: 1px 3px;
  }
  
  .port-info {
    font-size: 10px;
  }
  
  .port-type-indicator {
    width: 14px;
    height: 10px;
    font-size: 8px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .node-card {
    background: #262626;
    border-color: #434343;
    color: #f0f0f0;
  }
  
  .node-card:hover {
    background: #2f2f2f;
  }
  
  .node-name {
    color: #f0f0f0;
  }
  
  .port-name {
    color: #a6a6a6;
  }
  
  .port-label {
    background: rgba(38, 38, 38, 0.9);
    color: #f0f0f0;
  }
  
  .action-button:hover {
    background: #3a3a3a;
  }
  
  .delete-button:hover {
    background: #2a1f1f;
    color: #ff7875;
  }
  
  .custom-node.error .node-card {
    background: #2a1f1f;
    border-color: #ff7875;
  }
  
  .custom-node.warning .node-card {
    background: #2a2619;
    border-color: #ffc53d;
  }
  
  .custom-node.success .node-card {
    background: #1f2a1f;
    border-color: #73d13d;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .node-card {
    border-width: 2px;
  }
  
  .port-type-indicator {
    border: 1px solid #000;
  }
  
  .port-label {
    border: 1px solid #666;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .node-card,
  .action-button,
  .custom-node {
    transition: none;
  }
  
  .custom-node.executing {
    animation: none;
  }
  
  .react-flow__handle.connecting {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .node-actions {
    display: none;
  }
  
  .node-card {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .port-label {
    display: none;
  }
}
