/**
 * 视觉脚本编辑器示例应用
 * 展示完整的视觉脚本编程环境
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Layout, message, Button, Space, Modal, Input } from 'antd';
import { 
  SaveOutlined, 
  FolderOpenOutlined, 
  PlayCircleOutlined,
  FileAddOutlined,
  SettingOutlined
} from '@ant-design/icons';

import { NodePanel } from '../editor/NodePanel';
import { VisualCanvas } from '../editor/VisualCanvas';
import { PropertyEditor } from '../editor/PropertyEditor';
import { IVisualScriptNode, ScriptGraph } from '../core/types';
import { nodeRegistry } from '../nodes/registry/NodeRegistry';
import { registerBatch1CoreNodes } from '../nodes/registry/Batch1CoreNodes';
import { generateId } from '../../utils/IdGenerator';

import './VisualScriptEditor.css';

const { Header, Sider, Content } = Layout;
const { confirm } = Modal;

interface VisualScriptEditorProps {
  /** 初始脚本图 */
  initialGraph?: ScriptGraph;
  /** 脚本保存回调 */
  onSave?: (graph: ScriptGraph) => void;
  /** 脚本加载回调 */
  onLoad?: () => Promise<ScriptGraph | null>;
}

/**
 * 创建默认的空脚本图
 */
function createEmptyGraph(): ScriptGraph {
  return {
    id: generateId(),
    name: '新脚本',
    description: '使用视觉脚本编辑器创建的脚本',
    nodes: new Map(),
    connections: new Map(),
    variables: new Map(),
    metadata: {
      createdAt: Date.now(),
      version: '1.0.0'
    }
  };
}

/**
 * 视觉脚本编辑器主组件
 */
export const VisualScriptEditor: React.FC<VisualScriptEditorProps> = ({
  initialGraph,
  onSave,
  onLoad
}) => {
  const [currentGraph, setCurrentGraph] = useState<ScriptGraph>(
    initialGraph || createEmptyGraph()
  );
  const [selectedNode, setSelectedNode] = useState<IVisualScriptNode | null>(null);
  const [isModified, setIsModified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 初始化节点注册表
  useEffect(() => {
    try {
      registerBatch1CoreNodes();
      message.success('节点库加载完成！');
    } catch (error) {
      message.error(`节点库加载失败: ${error.message}`);
    }
  }, []);

  // 处理图形变化
  const handleGraphChange = useCallback((graph: ScriptGraph) => {
    setCurrentGraph(graph);
    setIsModified(true);
  }, []);

  // 处理节点选择
  const handleNodeSelect = useCallback((node: IVisualScriptNode | null) => {
    setSelectedNode(node);
  }, []);

  // 处理节点属性变化
  const handlePropertyChange = useCallback((property: string, value: any) => {
    if (selectedNode) {
      // 触发图形更新
      setIsModified(true);
    }
  }, [selectedNode]);

  // 保存脚本
  const handleSave = useCallback(async () => {
    try {
      setIsLoading(true);
      
      if (onSave) {
        await onSave(currentGraph);
      } else {
        // 默认保存到本地存储
        const savedGraphs = JSON.parse(localStorage.getItem('visualScripts') || '[]');
        const existingIndex = savedGraphs.findIndex((g: any) => g.id === currentGraph.id);
        
        const graphToSave = {
          ...currentGraph,
          nodes: Array.from(currentGraph.nodes.entries()),
          connections: Array.from(currentGraph.connections.entries()),
          variables: Array.from(currentGraph.variables.entries()),
          metadata: {
            ...currentGraph.metadata,
            updatedAt: Date.now()
          }
        };
        
        if (existingIndex >= 0) {
          savedGraphs[existingIndex] = graphToSave;
        } else {
          savedGraphs.push(graphToSave);
        }
        
        localStorage.setItem('visualScripts', JSON.stringify(savedGraphs));
      }
      
      setIsModified(false);
      message.success('脚本保存成功！');
    } catch (error) {
      message.error(`保存失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [currentGraph, onSave]);

  // 加载脚本
  const handleLoad = useCallback(async () => {
    if (isModified) {
      confirm({
        title: '未保存的更改',
        content: '当前脚本有未保存的更改，是否继续加载？',
        onOk: async () => {
          await loadScript();
        }
      });
    } else {
      await loadScript();
    }

    async function loadScript() {
      try {
        setIsLoading(true);
        
        let loadedGraph: ScriptGraph | null = null;
        
        if (onLoad) {
          loadedGraph = await onLoad();
        } else {
          // 默认从本地存储加载
          const savedGraphs = JSON.parse(localStorage.getItem('visualScripts') || '[]');
          if (savedGraphs.length > 0) {
            // 简单起见，加载最后一个保存的脚本
            const lastGraph = savedGraphs[savedGraphs.length - 1];
            loadedGraph = {
              ...lastGraph,
              nodes: new Map(lastGraph.nodes),
              connections: new Map(lastGraph.connections),
              variables: new Map(lastGraph.variables)
            };
          }
        }
        
        if (loadedGraph) {
          setCurrentGraph(loadedGraph);
          setSelectedNode(null);
          setIsModified(false);
          message.success('脚本加载成功！');
        } else {
          message.info('没有找到可加载的脚本');
        }
      } catch (error) {
        message.error(`加载失败: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    }
  }, [isModified, onLoad]);

  // 新建脚本
  const handleNew = useCallback(() => {
    if (isModified) {
      confirm({
        title: '未保存的更改',
        content: '当前脚本有未保存的更改，是否继续新建？',
        onOk: () => {
          setCurrentGraph(createEmptyGraph());
          setSelectedNode(null);
          setIsModified(false);
          message.success('新脚本已创建！');
        }
      });
    } else {
      setCurrentGraph(createEmptyGraph());
      setSelectedNode(null);
      setIsModified(false);
      message.success('新脚本已创建！');
    }
  }, [isModified]);

  // 重命名脚本
  const handleRename = useCallback(() => {
    Modal.confirm({
      title: '重命名脚本',
      content: (
        <Input
          defaultValue={currentGraph.name}
          placeholder="请输入脚本名称"
          id="script-name-input"
        />
      ),
      onOk: () => {
        const input = document.getElementById('script-name-input') as HTMLInputElement;
        const newName = input?.value?.trim();
        if (newName && newName !== currentGraph.name) {
          setCurrentGraph(prev => ({ ...prev, name: newName }));
          setIsModified(true);
          message.success('脚本已重命名！');
        }
      }
    });
  }, [currentGraph.name]);

  return (
    <div className="visual-script-editor">
      <Layout style={{ height: '100vh' }}>
        {/* 顶部工具栏 */}
        <Header className="editor-header">
          <div className="header-left">
            <h2 className="editor-title">
              {currentGraph.name}
              {isModified && <span className="modified-indicator">*</span>}
            </h2>
          </div>
          
          <div className="header-right">
            <Space>
              <Button 
                icon={<FileAddOutlined />} 
                onClick={handleNew}
              >
                新建
              </Button>
              
              <Button 
                icon={<FolderOpenOutlined />} 
                onClick={handleLoad}
                loading={isLoading}
              >
                加载
              </Button>
              
              <Button 
                type="primary"
                icon={<SaveOutlined />} 
                onClick={handleSave}
                loading={isLoading}
              >
                保存
              </Button>
              
              <Button 
                icon={<SettingOutlined />} 
                onClick={handleRename}
              >
                重命名
              </Button>
            </Space>
          </div>
        </Header>

        <Layout>
          {/* 左侧节点面板 */}
          <Sider 
            width={300} 
            className="node-panel-sider"
            theme="light"
          >
            <NodePanel
              onNodeDragStart={(nodeType) => {
                console.log('开始拖拽节点:', nodeType);
              }}
              onNodeDoubleClick={(nodeType) => {
                // 双击直接添加节点到画布中心
                const node = nodeRegistry.createNode(nodeType);
                if (node) {
                  node.position = { x: 400, y: 300 };
                  const newGraph = { ...currentGraph };
                  newGraph.nodes.set(node.id, node);
                  setCurrentGraph(newGraph);
                  setIsModified(true);
                  message.success(`已添加节点: ${node.name}`);
                }
              }}
            />
          </Sider>

          {/* 中央画布区域 */}
          <Content className="canvas-content">
            <VisualCanvas
              graph={currentGraph}
              onGraphChange={handleGraphChange}
              onNodeSelect={handleNodeSelect}
              width={800}
              height={600}
            />
          </Content>

          {/* 右侧属性编辑器 */}
          <Sider 
            width={300} 
            className="property-panel-sider"
            theme="light"
          >
            <PropertyEditor
              selectedNode={selectedNode}
              onPropertyChange={handlePropertyChange}
            />
          </Sider>
        </Layout>
      </Layout>
    </div>
  );
};

export default VisualScriptEditor;
