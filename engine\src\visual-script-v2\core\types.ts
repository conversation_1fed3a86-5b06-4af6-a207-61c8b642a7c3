/**
 * 视觉脚本系统核心类型定义
 * 统一的类型系统，避免多套系统的类型冲突
 */

import { Entity, World } from '../../../ecs';

/**
 * 数据类型枚举
 */
export enum DataType {
  // 基础类型
  TRIGGER = 'trigger',
  BOOLEAN = 'boolean',
  NUMBER = 'number',
  STRING = 'string',
  OBJECT = 'object',
  ARRAY = 'array',
  
  // 数学类型
  VECTOR2 = 'vector2',
  VECTOR3 = 'vector3',
  VECTOR4 = 'vector4',
  QUATERNION = 'quaternion',
  MATRIX3 = 'matrix3',
  MATRIX4 = 'matrix4',
  COLOR = 'color',
  
  // 引擎类型
  ENTITY = 'entity',
  COMPONENT = 'component',
  MATERIAL = 'material',
  TEXTURE = 'texture',
  MESH = 'mesh',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  
  // 高级类型
  FUNCTION = 'function',
  EVENT = 'event',
  PROMISE = 'promise',
  STREAM = 'stream',
  
  // 任意类型
  ANY = 'any'
}

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  // 核心系统
  CORE = 'core',
  EVENTS = 'events',
  FLOW_CONTROL = 'flow_control',
  MATH = 'math',
  LOGIC = 'logic',
  DATA = 'data',
  
  // 渲染系统
  RENDERING = 'rendering',
  MATERIAL = 'material',
  LIGHTING = 'lighting',
  CAMERA = 'camera',
  POST_PROCESSING = 'post_processing',
  
  // 物理系统
  PHYSICS = 'physics',
  COLLISION = 'collision',
  CONSTRAINTS = 'constraints',
  
  // 动画系统
  ANIMATION = 'animation',
  TWEENING = 'tweening',
  PARTICLES = 'particles',
  
  // AI系统
  AI = 'ai',
  MACHINE_LEARNING = 'machine_learning',
  COMPUTER_VISION = 'computer_vision',
  NATURAL_LANGUAGE = 'natural_language',
  
  // 网络系统
  NETWORK = 'network',
  HTTP = 'http',
  WEBSOCKET = 'websocket',
  DATABASE = 'database',
  
  // UI系统
  UI = 'ui',
  LAYOUT = 'layout',
  INTERACTION = 'interaction',
  
  // 音频系统
  AUDIO = 'audio',
  SOUND_EFFECTS = 'sound_effects',
  MUSIC = 'music',
  
  // 输入系统
  INPUT = 'input',
  KEYBOARD = 'keyboard',
  MOUSE = 'mouse',
  TOUCH = 'touch',
  VR = 'vr',
  
  // 工具系统
  TOOLS = 'tools',
  DEBUG = 'debug',
  UTILITY = 'utility'
}

/**
 * 节点端口定义
 */
export interface NodePort {
  /** 端口名称 */
  name: string;
  /** 端口显示标签 */
  label: string;
  /** 数据类型 */
  type: DataType;
  /** 端口方向 */
  direction: 'input' | 'output';
  /** 是否必需 */
  required: boolean;
  /** 默认值 */
  defaultValue?: any;
  /** 描述信息 */
  description?: string;
  /** 是否支持多连接 */
  multiple?: boolean;
}

/**
 * 节点连接定义
 */
export interface NodeConnection {
  /** 连接ID */
  id: string;
  /** 源节点ID */
  fromNodeId: string;
  /** 源端口名称 */
  fromPort: string;
  /** 目标节点ID */
  toNodeId: string;
  /** 目标端口名称 */
  toPort: string;
  /** 连接类型 */
  type: DataType;
}

/**
 * 节点定义
 */
export interface NodeDefinition {
  /** 节点类型 */
  type: string;
  /** 节点名称 */
  name: string;
  /** 节点描述 */
  description: string;
  /** 节点分类 */
  category: NodeCategory;
  /** 节点图标 */
  icon?: string;
  /** 节点颜色 */
  color?: string;
  /** 节点标签 */
  tags?: string[];
  /** 输入端口定义 */
  inputs: NodePort[];
  /** 输出端口定义 */
  outputs: NodePort[];
  /** 节点构造函数 */
  nodeClass: new (id?: string) => IVisualScriptNode;
  /** 是否为异步节点 */
  async?: boolean;
  /** 节点优先级 */
  priority?: number;
}

/**
 * 执行上下文接口
 */
export interface IExecutionContext {
  /** 获取输入值 */
  getInputValue(portName: string): any;
  /** 设置输出值 */
  setOutputValue(portName: string, value: any): void;
  /** 获取变量 */
  getVariable(name: string): any;
  /** 设置变量 */
  setVariable(name: string, value: any): void;
  /** 获取实体 */
  getEntity(): Entity | null;
  /** 获取世界 */
  getWorld(): World | null;
  /** 获取引擎服务 */
  getService<T>(serviceName: string): T | null;
  /** 触发事件 */
  triggerEvent(eventName: string, data?: any): void;
  /** 监听事件 */
  addEventListener(eventName: string, callback: (data: any) => void): void;
  /** 移除事件监听 */
  removeEventListener(eventName: string, callback: (data: any) => void): void;
  /** 获取时间信息 */
  getTime(): { deltaTime: number; totalTime: number; frameCount: number };
  /** 日志输出 */
  log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]): void;
}

/**
 * 视觉脚本节点接口
 */
export interface IVisualScriptNode {
  /** 节点ID */
  readonly id: string;
  /** 节点类型 */
  readonly type: string;
  /** 节点名称 */
  name: string;
  /** 节点分类 */
  readonly category: NodeCategory;
  /** 节点位置 */
  position: { x: number; y: number };
  /** 是否启用 */
  enabled: boolean;
  
  /** 获取输入端口 */
  getInputPorts(): NodePort[];
  /** 获取输出端口 */
  getOutputPorts(): NodePort[];
  /** 获取输入端口 */
  getInputPort(name: string): NodePort | null;
  /** 获取输出端口 */
  getOutputPort(name: string): NodePort | null;
  
  /** 执行节点 */
  execute(context: IExecutionContext): Promise<void>;
  /** 验证节点 */
  validate(): { isValid: boolean; errors: string[] };
  /** 序列化节点 */
  serialize(): any;
  /** 反序列化节点 */
  deserialize(data: any): void;
  /** 克隆节点 */
  clone(): IVisualScriptNode;
  /** 销毁节点 */
  dispose(): void;
  
  /** 获取可编辑属性 */
  getEditableProperties(): NodeProperty[];
  /** 设置属性值 */
  setProperty(name: string, value: any): void;
  /** 获取属性值 */
  getProperty(name: string): any;
}

/**
 * 节点属性定义
 */
export interface NodeProperty {
  /** 属性名称 */
  name: string;
  /** 属性标签 */
  label: string;
  /** 属性类型 */
  type: 'string' | 'number' | 'boolean' | 'select' | 'color' | 'vector' | 'file';
  /** 属性值 */
  value: any;
  /** 默认值 */
  defaultValue?: any;
  /** 属性描述 */
  description?: string;
  /** 是否只读 */
  readonly?: boolean;
  /** 选项列表（用于select类型） */
  options?: { label: string; value: any }[];
  /** 最小值（用于number类型） */
  min?: number;
  /** 最大值（用于number类型） */
  max?: number;
  /** 步长（用于number类型） */
  step?: number;
}

/**
 * 脚本图定义
 */
export interface ScriptGraph {
  /** 图ID */
  id: string;
  /** 图名称 */
  name: string;
  /** 图描述 */
  description?: string;
  /** 节点列表 */
  nodes: Map<string, IVisualScriptNode>;
  /** 连接列表 */
  connections: Map<string, NodeConnection>;
  /** 变量定义 */
  variables: Map<string, { type: DataType; value: any; description?: string }>;
  /** 图元数据 */
  metadata: { [key: string]: any };
}

/**
 * 执行结果
 */
export interface ExecutionResult {
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 执行时间（毫秒） */
  executionTime: number;
  /** 执行的节点数量 */
  nodesExecuted: number;
  /** 输出数据 */
  outputs?: { [key: string]: any };
}

/**
 * 调试信息
 */
export interface DebugInfo {
  /** 节点ID */
  nodeId: string;
  /** 执行状态 */
  status: 'pending' | 'running' | 'completed' | 'error';
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime?: number;
  /** 输入数据 */
  inputs: { [key: string]: any };
  /** 输出数据 */
  outputs: { [key: string]: any };
  /** 错误信息 */
  error?: string;
}

/**
 * 性能统计
 */
export interface PerformanceStats {
  /** 总执行时间 */
  totalExecutionTime: number;
  /** 节点执行统计 */
  nodeStats: Map<string, {
    count: number;
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
  }>;
  /** 内存使用情况 */
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  /** 帧率统计 */
  frameStats: {
    fps: number;
    frameTime: number;
    minFrameTime: number;
    maxFrameTime: number;
  };
}

/**
 * 事件定义
 */
export interface ScriptEvent {
  /** 事件名称 */
  name: string;
  /** 事件数据 */
  data?: any;
  /** 事件时间戳 */
  timestamp: number;
  /** 事件源节点ID */
  sourceNodeId?: string;
  /** 事件目标节点ID */
  targetNodeId?: string;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误列表 */
  errors: string[];
  /** 警告列表 */
  warnings: string[];
  /** 建议列表 */
  suggestions: string[];
}
