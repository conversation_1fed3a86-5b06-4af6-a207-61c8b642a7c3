/**
 * 节点面板组件
 * 显示所有可用节点，支持搜索和分类过滤
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Input, Tabs, Card, Empty, Badge, Tooltip } from 'antd';
import { SearchOutlined, StarOutlined, StarFilled } from '@ant-design/icons';
import { NodeDefinition, NodeCategory } from '../core/types';
import { nodeRegistry } from '../nodes/registry/NodeRegistry';
import './NodePanel.css';

const { TabPane } = Tabs;
const { Search } = Input;

interface NodePanelProps {
  /** 节点拖拽开始回调 */
  onNodeDragStart?: (nodeType: string) => void;
  /** 节点双击回调 */
  onNodeDoubleClick?: (nodeType: string) => void;
  /** 面板宽度 */
  width?: number;
  /** 是否显示收藏功能 */
  showFavorites?: boolean;
}

interface DraggableNodeProps {
  definition: NodeDefinition;
  isFavorite: boolean;
  onDragStart: (nodeType: string) => void;
  onDoubleClick: (nodeType: string) => void;
  onToggleFavorite: (nodeType: string) => void;
}

/**
 * 可拖拽的节点项组件
 */
const DraggableNode: React.FC<DraggableNodeProps> = ({
  definition,
  isFavorite,
  onDragStart,
  onDoubleClick,
  onToggleFavorite
}) => {
  const handleDragStart = useCallback((e: React.DragEvent) => {
    e.dataTransfer.setData('nodeType', definition.type);
    e.dataTransfer.effectAllowed = 'copy';
    onDragStart(definition.type);
  }, [definition.type, onDragStart]);

  const handleDoubleClick = useCallback(() => {
    onDoubleClick(definition.type);
  }, [definition.type, onDoubleClick]);

  const handleToggleFavorite = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(definition.type);
  }, [definition.type, onToggleFavorite]);

  return (
    <Card
      size="small"
      className={`node-item ${isFavorite ? 'favorite' : ''}`}
      draggable
      onDragStart={handleDragStart}
      onDoubleClick={handleDoubleClick}
      style={{ 
        borderColor: definition.color,
        cursor: 'grab'
      }}
      bodyStyle={{ padding: '8px 12px' }}
      actions={[
        <Tooltip title={isFavorite ? '取消收藏' : '添加收藏'} key="favorite">
          <div onClick={handleToggleFavorite}>
            {isFavorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
          </div>
        </Tooltip>
      ]}
    >
      <div className="node-item-content">
        <div className="node-header">
          <span className="node-icon" style={{ fontSize: '16px' }}>
            {definition.icon || '📦'}
          </span>
          <span className="node-name">{definition.name}</span>
        </div>
        <div className="node-description">
          {definition.description}
        </div>
        <div className="node-tags">
          {definition.tags?.map(tag => (
            <Badge key={tag} count={tag} style={{ backgroundColor: '#f0f0f0', color: '#666' }} />
          ))}
        </div>
      </div>
    </Card>
  );
};

/**
 * 节点面板主组件
 */
export const NodePanel: React.FC<NodePanelProps> = ({
  onNodeDragStart = () => {},
  onNodeDoubleClick = () => {},
  width = 300,
  showFavorites = true
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [favoriteNodes, setFavoriteNodes] = useState<Set<string>>(new Set());

  // 获取所有节点定义
  const allNodes = useMemo(() => {
    return nodeRegistry.getAllDefinitions();
  }, []);

  // 获取所有分类
  const categories = useMemo(() => {
    const cats = nodeRegistry.getCategories();
    return [
      { key: 'all', label: '全部', count: allNodes.length },
      { key: 'favorites', label: '收藏', count: favoriteNodes.size },
      ...cats.map(cat => ({
        key: cat,
        label: getCategoryLabel(cat),
        count: nodeRegistry.getByCategory(cat).length
      }))
    ];
  }, [allNodes.length, favoriteNodes.size]);

  // 过滤节点
  const filteredNodes = useMemo(() => {
    let nodes = allNodes;

    // 分类过滤
    if (selectedCategory === 'favorites') {
      nodes = nodes.filter(node => favoriteNodes.has(node.type));
    } else if (selectedCategory !== 'all') {
      nodes = nodes.filter(node => node.category === selectedCategory);
    }

    // 搜索过滤
    if (searchTerm) {
      const query = searchTerm.toLowerCase();
      nodes = nodes.filter(node => {
        const searchText = `${node.name} ${node.description} ${node.type} ${(node.tags || []).join(' ')}`.toLowerCase();
        return searchText.includes(query);
      });
    }

    // 按优先级和名称排序
    return nodes.sort((a, b) => {
      const priorityDiff = (b.priority || 0) - (a.priority || 0);
      if (priorityDiff !== 0) return priorityDiff;
      return a.name.localeCompare(b.name);
    });
  }, [allNodes, selectedCategory, searchTerm, favoriteNodes]);

  // 切换收藏状态
  const toggleFavorite = useCallback((nodeType: string) => {
    setFavoriteNodes(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(nodeType)) {
        newFavorites.delete(nodeType);
      } else {
        newFavorites.add(nodeType);
      }
      // 保存到本地存储
      localStorage.setItem('visualScript.favoriteNodes', JSON.stringify(Array.from(newFavorites)));
      return newFavorites;
    });
  }, []);

  // 从本地存储加载收藏
  React.useEffect(() => {
    try {
      const saved = localStorage.getItem('visualScript.favoriteNodes');
      if (saved) {
        setFavoriteNodes(new Set(JSON.parse(saved)));
      }
    } catch (error) {
      console.warn('加载收藏节点失败:', error);
    }
  }, []);

  return (
    <div className="node-panel" style={{ width }}>
      {/* 搜索框 */}
      <div className="search-section">
        <Search
          placeholder="搜索节点..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          prefix={<SearchOutlined />}
          allowClear
        />
      </div>

      {/* 分类标签 */}
      <div className="category-section">
        <Tabs
          activeKey={selectedCategory}
          onChange={setSelectedCategory}
          size="small"
          type="card"
        >
          {categories.map(category => (
            <TabPane
              tab={
                <span>
                  {category.label}
                  <Badge count={category.count} style={{ marginLeft: 8 }} />
                </span>
              }
              key={category.key}
            />
          ))}
        </Tabs>
      </div>

      {/* 节点列表 */}
      <div className="nodes-section">
        {filteredNodes.length === 0 ? (
          <Empty
            description={searchTerm ? '没有找到匹配的节点' : '此分类下没有节点'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div className="nodes-list">
            {filteredNodes.map(node => (
              <DraggableNode
                key={node.type}
                definition={node}
                isFavorite={favoriteNodes.has(node.type)}
                onDragStart={onNodeDragStart}
                onDoubleClick={onNodeDoubleClick}
                onToggleFavorite={toggleFavorite}
              />
            ))}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="stats-section">
        <div className="stats-item">
          <span>总节点数: {allNodes.length}</span>
        </div>
        <div className="stats-item">
          <span>当前显示: {filteredNodes.length}</span>
        </div>
        {showFavorites && (
          <div className="stats-item">
            <span>收藏数: {favoriteNodes.size}</span>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 获取分类显示标签
 */
function getCategoryLabel(category: NodeCategory): string {
  const labels: Record<NodeCategory, string> = {
    [NodeCategory.CORE]: '核心',
    [NodeCategory.EVENTS]: '事件',
    [NodeCategory.FLOW_CONTROL]: '流程控制',
    [NodeCategory.MATH]: '数学',
    [NodeCategory.LOGIC]: '逻辑',
    [NodeCategory.DATA]: '数据',
    [NodeCategory.RENDERING]: '渲染',
    [NodeCategory.MATERIAL]: '材质',
    [NodeCategory.LIGHTING]: '光照',
    [NodeCategory.CAMERA]: '相机',
    [NodeCategory.POST_PROCESSING]: '后处理',
    [NodeCategory.PHYSICS]: '物理',
    [NodeCategory.COLLISION]: '碰撞',
    [NodeCategory.CONSTRAINTS]: '约束',
    [NodeCategory.ANIMATION]: '动画',
    [NodeCategory.TWEENING]: '补间',
    [NodeCategory.PARTICLES]: '粒子',
    [NodeCategory.AI]: 'AI',
    [NodeCategory.MACHINE_LEARNING]: '机器学习',
    [NodeCategory.COMPUTER_VISION]: '计算机视觉',
    [NodeCategory.NATURAL_LANGUAGE]: '自然语言',
    [NodeCategory.NETWORK]: '网络',
    [NodeCategory.HTTP]: 'HTTP',
    [NodeCategory.WEBSOCKET]: 'WebSocket',
    [NodeCategory.DATABASE]: '数据库',
    [NodeCategory.UI]: 'UI',
    [NodeCategory.LAYOUT]: '布局',
    [NodeCategory.INTERACTION]: '交互',
    [NodeCategory.AUDIO]: '音频',
    [NodeCategory.SOUND_EFFECTS]: '音效',
    [NodeCategory.MUSIC]: '音乐',
    [NodeCategory.INPUT]: '输入',
    [NodeCategory.KEYBOARD]: '键盘',
    [NodeCategory.MOUSE]: '鼠标',
    [NodeCategory.TOUCH]: '触摸',
    [NodeCategory.VR]: 'VR',
    [NodeCategory.TOOLS]: '工具',
    [NodeCategory.DEBUG]: '调试',
    [NodeCategory.UTILITY]: '实用工具'
  };

  return labels[category] || category;
}

export default NodePanel;
