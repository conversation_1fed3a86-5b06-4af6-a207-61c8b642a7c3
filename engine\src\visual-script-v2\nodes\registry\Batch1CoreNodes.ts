/**
 * 批次1：核心基础节点注册
 * 包含50个最基础的核心功能节点
 */

import { NodeDefinition, NodeCategory, DataType } from '../../core/types';
import { nodeRegistry } from './NodeRegistry';

// 导入事件节点
import {
  OnStartNode,
  OnUpdateNode,
  OnDestroyNode,
  OnClickNode,
  OnHoverNode,
  OnKeyNode,
  CustomEventNode
} from '../categories/events/OnStartNode';

// 导入流程控制节点
import {
  SequenceNode,
  BranchNode,
  ForLoopNode,
  WhileLoopNode,
  DelayNode,
  SwitchNode,
  GateNode,
  DoOnceNode
} from '../categories/flow/FlowControlNodes';

// 导入数学运算节点
import {
  AddNode,
  SubtractNode,
  MultiplyNode,
  DivideNode,
  ModuloNode,
  PowerNode,
  SquareRootNode,
  AbsNode,
  MinNode,
  MaxNode,
  ClampNode,
  LerpNode,
  SinNode,
  CosNode,
  RandomNode
} from '../categories/math/MathNodes';

// 导入逻辑运算节点
import {
  AndNode,
  OrNode,
  NotNode,
  EqualNode,
  NotEqualNode,
  GreaterNode,
  LessNode,
  GreaterEqualNode,
  LessEqualNode,
  IsValidNode
} from '../categories/logic/LogicNodes';

// 导入数据操作节点
import {
  SetVariableNode,
  GetVariableNode,
  CreateArrayNode,
  ArrayPushNode,
  ArrayGetNode,
  ArrayLengthNode,
  CreateObjectNode,
  GetObjectPropertyNode,
  SetObjectPropertyNode
} from '../categories/data/DataNodes';

/**
 * 事件节点定义 (10个)
 */
const eventNodeDefinitions: NodeDefinition[] = [
  {
    type: 'events/onStart',
    name: '开始事件',
    description: '脚本开始执行时触发',
    category: NodeCategory.EVENTS,
    icon: '▶️',
    color: '#4CAF50',
    tags: ['事件', '开始', '触发'],
    inputs: [],
    outputs: [
      {
        name: 'onStart',
        label: '开始',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '脚本开始时触发'
      }
    ],
    nodeClass: OnStartNode,
    priority: 1
  },
  {
    type: 'events/onUpdate',
    name: '更新事件',
    description: '每帧更新时触发',
    category: NodeCategory.EVENTS,
    icon: '🔄',
    color: '#2196F3',
    tags: ['事件', '更新', '帧'],
    inputs: [],
    outputs: [
      {
        name: 'onUpdate',
        label: '更新',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每帧更新时触发'
      },
      {
        name: 'deltaTime',
        label: '帧时间',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前帧的时间间隔'
      }
    ],
    nodeClass: OnUpdateNode,
    priority: 1
  },
  {
    type: 'events/onDestroy',
    name: '销毁事件',
    description: '脚本销毁时触发',
    category: NodeCategory.EVENTS,
    icon: '🗑️',
    color: '#F44336',
    tags: ['事件', '销毁', '清理'],
    inputs: [],
    outputs: [
      {
        name: 'onDestroy',
        label: '销毁',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '脚本销毁时触发'
      }
    ],
    nodeClass: OnDestroyNode,
    priority: 1
  },
  {
    type: 'events/onClick',
    name: '点击事件',
    description: '鼠标点击时触发',
    category: NodeCategory.EVENTS,
    icon: '👆',
    color: '#FF9800',
    tags: ['事件', '点击', '鼠标', '交互'],
    inputs: [
      {
        name: 'target',
        label: '目标对象',
        type: DataType.ENTITY,
        direction: 'input',
        required: false,
        description: '监听点击的目标对象'
      }
    ],
    outputs: [
      {
        name: 'onClick',
        label: '点击',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '点击时触发'
      },
      {
        name: 'position',
        label: '点击位置',
        type: DataType.VECTOR2,
        direction: 'output',
        required: false,
        description: '点击的屏幕坐标'
      }
    ],
    nodeClass: OnClickNode,
    priority: 2
  },
  {
    type: 'events/onHover',
    name: '悬停事件',
    description: '鼠标悬停时触发',
    category: NodeCategory.EVENTS,
    icon: '👋',
    color: '#9C27B0',
    tags: ['事件', '悬停', '鼠标', '交互'],
    inputs: [
      {
        name: 'target',
        label: '目标对象',
        type: DataType.ENTITY,
        direction: 'input',
        required: false,
        description: '监听悬停的目标对象'
      }
    ],
    outputs: [
      {
        name: 'onHoverEnter',
        label: '悬停进入',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '鼠标进入时触发'
      },
      {
        name: 'onHoverExit',
        label: '悬停离开',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '鼠标离开时触发'
      }
    ],
    nodeClass: OnHoverNode,
    priority: 2
  },
  {
    type: 'events/onKey',
    name: '按键事件',
    description: '键盘按键时触发',
    category: NodeCategory.EVENTS,
    icon: '⌨️',
    color: '#607D8B',
    tags: ['事件', '按键', '键盘', '输入'],
    inputs: [],
    outputs: [
      {
        name: 'onKeyDown',
        label: '按键按下',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '按键按下时触发'
      },
      {
        name: 'onKeyUp',
        label: '按键释放',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '按键释放时触发'
      },
      {
        name: 'key',
        label: '按键代码',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '按下的按键代码'
      }
    ],
    nodeClass: OnKeyNode,
    priority: 2
  },
  {
    type: 'events/customEvent',
    name: '自定义事件',
    description: '监听自定义事件',
    category: NodeCategory.EVENTS,
    icon: '📡',
    color: '#795548',
    tags: ['事件', '自定义', '监听'],
    inputs: [],
    outputs: [
      {
        name: 'onEvent',
        label: '事件触发',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '自定义事件触发时执行'
      },
      {
        name: 'eventData',
        label: '事件数据',
        type: DataType.OBJECT,
        direction: 'output',
        required: false,
        description: '事件携带的数据'
      }
    ],
    nodeClass: CustomEventNode,
    priority: 3
  }
];

/**
 * 流程控制节点定义 (15个)
 */
const flowControlNodeDefinitions: NodeDefinition[] = [
  {
    type: 'flow/sequence',
    name: '顺序执行',
    description: '按顺序执行多个输出',
    category: NodeCategory.FLOW_CONTROL,
    icon: '📋',
    color: '#3F51B5',
    tags: ['流程', '顺序', '执行'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发顺序执行'
      }
    ],
    outputs: [
      {
        name: 'then1',
        label: '然后1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个执行输出'
      },
      {
        name: 'then2',
        label: '然后2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个执行输出'
      },
      {
        name: 'then3',
        label: '然后3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第三个执行输出'
      }
    ],
    nodeClass: SequenceNode,
    priority: 1
  },
  {
    type: 'flow/branch',
    name: '条件分支',
    description: '根据条件选择执行路径',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🔀',
    color: '#FF5722',
    tags: ['流程', '条件', '分支', '判断'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发条件判断'
      },
      {
        name: 'condition',
        label: '条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        defaultValue: false,
        description: '判断条件'
      }
    ],
    outputs: [
      {
        name: 'true',
        label: '真',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件为真时执行'
      },
      {
        name: 'false',
        label: '假',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件为假时执行'
      }
    ],
    nodeClass: BranchNode,
    priority: 1
  },
  {
    type: 'flow/forLoop',
    name: 'For循环',
    description: '执行指定次数的循环',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🔁',
    color: '#009688',
    tags: ['流程', '循环', 'for', '迭代'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始循环'
      },
      {
        name: 'startIndex',
        label: '起始索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '循环起始索引'
      },
      {
        name: 'endIndex',
        label: '结束索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10,
        description: '循环结束索引'
      }
    ],
    outputs: [
      {
        name: 'loopBody',
        label: '循环体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次循环执行'
      },
      {
        name: 'index',
        label: '当前索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前循环索引'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '循环完成时执行'
      }
    ],
    nodeClass: ForLoopNode,
    priority: 2
  },
  {
    type: 'flow/delay',
    name: '延迟',
    description: '延迟指定时间后执行',
    category: NodeCategory.FLOW_CONTROL,
    icon: '⏰',
    color: '#FFC107',
    tags: ['流程', '延迟', '时间', '等待'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始延迟'
      },
      {
        name: 'duration',
        label: '延迟时间',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1.0,
        description: '延迟时间（秒）'
      }
    ],
    outputs: [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '延迟完成后执行'
      }
    ],
    nodeClass: DelayNode,
    priority: 2
  },
  {
    type: 'flow/whileLoop',
    name: 'While循环',
    description: '根据条件执行循环',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🔁',
    color: '#009688',
    tags: ['流程', '循环', 'while', '条件'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始循环'
      },
      {
        name: 'condition',
        label: '条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        defaultValue: false,
        description: '循环条件'
      }
    ],
    outputs: [
      {
        name: 'loopBody',
        label: '循环体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次循环执行'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '循环完成时执行'
      }
    ],
    nodeClass: WhileLoopNode,
    priority: 2
  },
  {
    type: 'flow/switch',
    name: '开关选择',
    description: '根据索引选择执行路径',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🔀',
    color: '#795548',
    tags: ['流程', '选择', '开关'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发开关选择'
      },
      {
        name: 'selection',
        label: '选择索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: true,
        defaultValue: 0,
        description: '选择的输出索引'
      }
    ],
    outputs: [
      {
        name: 'option0',
        label: '选项0',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '索引为0时执行'
      },
      {
        name: 'option1',
        label: '选项1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '索引为1时执行'
      },
      {
        name: 'default',
        label: '默认',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '没有匹配选项时执行'
      }
    ],
    nodeClass: SwitchNode,
    priority: 2
  },
  {
    type: 'flow/gate',
    name: '门控制',
    description: '控制信号的通过',
    category: NodeCategory.FLOW_CONTROL,
    icon: '🚪',
    color: '#607D8B',
    tags: ['流程', '门', '控制'],
    inputs: [
      {
        name: 'enter',
        label: '进入',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '尝试通过门的信号'
      },
      {
        name: 'open',
        label: '打开',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '打开门'
      },
      {
        name: 'close',
        label: '关闭',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '关闭门'
      }
    ],
    outputs: [
      {
        name: 'exit',
        label: '退出',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '信号通过门后的输出'
      }
    ],
    nodeClass: GateNode,
    priority: 2
  },
  {
    type: 'flow/doOnce',
    name: '执行一次',
    description: '只执行一次，后续调用被忽略',
    category: NodeCategory.FLOW_CONTROL,
    icon: '1️⃣',
    color: '#E91E63',
    tags: ['流程', '一次', '限制'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发执行'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置执行状态'
      }
    ],
    outputs: [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一次执行时触发'
      }
    ],
    nodeClass: DoOnceNode,
    priority: 2
  }
];

/**
 * 数学运算节点定义 (15个)
 */
const mathNodeDefinitions: NodeDefinition[] = [
  {
    type: 'math/add',
    name: '加法',
    description: '计算两个数值的和',
    category: NodeCategory.MATH,
    icon: '➕',
    color: '#4CAF50',
    tags: ['数学', '加法', '运算'],
    inputs: [
      {
        name: 'a',
        label: '数值A',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '第一个加数'
      },
      {
        name: 'b',
        label: '数值B',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '第二个加数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '加法运算结果'
      }
    ],
    nodeClass: AddNode,
    priority: 1
  },
  {
    type: 'math/subtract',
    name: '减法',
    description: '计算两个数值的差',
    category: NodeCategory.MATH,
    icon: '➖',
    color: '#F44336',
    tags: ['数学', '减法', '运算'],
    inputs: [
      {
        name: 'a',
        label: '被减数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '被减数'
      },
      {
        name: 'b',
        label: '减数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '减数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '减法运算结果'
      }
    ],
    nodeClass: SubtractNode,
    priority: 1
  },
  {
    type: 'math/multiply',
    name: '乘法',
    description: '计算两个数值的积',
    category: NodeCategory.MATH,
    icon: '✖️',
    color: '#2196F3',
    tags: ['数学', '乘法', '运算'],
    inputs: [
      {
        name: 'a',
        label: '乘数A',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1,
        description: '第一个乘数'
      },
      {
        name: 'b',
        label: '乘数B',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1,
        description: '第二个乘数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '乘法运算结果'
      }
    ],
    nodeClass: MultiplyNode,
    priority: 1
  },
  {
    type: 'math/divide',
    name: '除法',
    description: '计算两个数值的商',
    category: NodeCategory.MATH,
    icon: '➗',
    color: '#FF9800',
    tags: ['数学', '除法', '运算'],
    inputs: [
      {
        name: 'a',
        label: '被除数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1,
        description: '被除数'
      },
      {
        name: 'b',
        label: '除数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1,
        description: '除数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '除法运算结果'
      }
    ],
    nodeClass: DivideNode,
    priority: 1
  },
  {
    type: 'math/modulo',
    name: '取模',
    description: '计算两个数值的余数',
    category: NodeCategory.MATH,
    icon: '📐',
    color: '#9C27B0',
    tags: ['数学', '取模', '余数'],
    inputs: [
      {
        name: 'a',
        label: '被除数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10,
        description: '被除数'
      },
      {
        name: 'b',
        label: '除数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 3,
        description: '除数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '余数',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '取模运算结果'
      }
    ],
    nodeClass: ModuloNode,
    priority: 1
  },
  {
    type: 'math/power',
    name: '幂运算',
    description: '计算数值的幂',
    category: NodeCategory.MATH,
    icon: '🔺',
    color: '#E91E63',
    tags: ['数学', '幂', '指数'],
    inputs: [
      {
        name: 'base',
        label: '底数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 2,
        description: '底数'
      },
      {
        name: 'exponent',
        label: '指数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 2,
        description: '指数'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '幂运算结果'
      }
    ],
    nodeClass: PowerNode,
    priority: 2
  },
  {
    type: 'math/sqrt',
    name: '平方根',
    description: '计算数值的平方根',
    category: NodeCategory.MATH,
    icon: '√',
    color: '#795548',
    tags: ['数学', '平方根', '开方'],
    inputs: [
      {
        name: 'value',
        label: '数值',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 4,
        description: '要计算平方根的数值'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '平方根结果'
      }
    ],
    nodeClass: SquareRootNode,
    priority: 2
  }
];

/**
 * 逻辑运算节点定义 (10个)
 */
const logicNodeDefinitions: NodeDefinition[] = [
  {
    type: 'logic/and',
    name: '逻辑与',
    description: '计算两个布尔值的逻辑与',
    category: NodeCategory.LOGIC,
    icon: '&',
    color: '#3F51B5',
    tags: ['逻辑', '与', '布尔'],
    inputs: [
      {
        name: 'a',
        label: '条件A',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: false,
        defaultValue: false,
        description: '第一个布尔值'
      },
      {
        name: 'b',
        label: '条件B',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: false,
        defaultValue: false,
        description: '第二个布尔值'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '逻辑与运算结果'
      }
    ],
    nodeClass: AndNode,
    priority: 1
  },
  {
    type: 'logic/or',
    name: '逻辑或',
    description: '计算两个布尔值的逻辑或',
    category: NodeCategory.LOGIC,
    icon: '|',
    color: '#FF5722',
    tags: ['逻辑', '或', '布尔'],
    inputs: [
      {
        name: 'a',
        label: '条件A',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: false,
        defaultValue: false,
        description: '第一个布尔值'
      },
      {
        name: 'b',
        label: '条件B',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: false,
        defaultValue: false,
        description: '第二个布尔值'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '逻辑或运算结果'
      }
    ],
    nodeClass: OrNode,
    priority: 1
  },
  {
    type: 'logic/not',
    name: '逻辑非',
    description: '计算布尔值的逻辑非',
    category: NodeCategory.LOGIC,
    icon: '!',
    color: '#E91E63',
    tags: ['逻辑', '非', '取反'],
    inputs: [
      {
        name: 'value',
        label: '输入值',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: false,
        defaultValue: false,
        description: '要取反的布尔值'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '逻辑非运算结果'
      }
    ],
    nodeClass: NotNode,
    priority: 1
  },
  {
    type: 'logic/equal',
    name: '等于',
    description: '比较两个值是否相等',
    category: NodeCategory.LOGIC,
    icon: '=',
    color: '#4CAF50',
    tags: ['逻辑', '比较', '相等'],
    inputs: [
      {
        name: 'a',
        label: '值A',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '第一个比较值'
      },
      {
        name: 'b',
        label: '值B',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '第二个比较值'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '比较结果'
      }
    ],
    nodeClass: EqualNode,
    priority: 1
  },
  {
    type: 'logic/greater',
    name: '大于',
    description: '比较第一个值是否大于第二个值',
    category: NodeCategory.LOGIC,
    icon: '>',
    color: '#FF9800',
    tags: ['逻辑', '比较', '大于'],
    inputs: [
      {
        name: 'a',
        label: '值A',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '第一个数值'
      },
      {
        name: 'b',
        label: '值B',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '第二个数值'
      }
    ],
    outputs: [
      {
        name: 'result',
        label: '结果',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: 'A > B 的比较结果'
      }
    ],
    nodeClass: GreaterNode,
    priority: 1
  }
];

/**
 * 数据操作节点定义 (10个)
 */
const dataNodeDefinitions: NodeDefinition[] = [
  {
    type: 'data/setVariable',
    name: '设置变量',
    description: '设置一个变量的值',
    category: NodeCategory.DATA,
    icon: '📝',
    color: '#9C27B0',
    tags: ['数据', '变量', '设置'],
    inputs: [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发设置变量'
      },
      {
        name: 'value',
        label: '值',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要设置的值'
      }
    ],
    outputs: [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '设置完成后触发'
      },
      {
        name: 'value',
        label: '输出值',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '设置的值'
      }
    ],
    nodeClass: SetVariableNode,
    priority: 1
  },
  {
    type: 'data/getVariable',
    name: '获取变量',
    description: '获取一个变量的值',
    category: NodeCategory.DATA,
    icon: '📖',
    color: '#607D8B',
    tags: ['数据', '变量', '获取'],
    inputs: [],
    outputs: [
      {
        name: 'value',
        label: '值',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '变量的值'
      }
    ],
    nodeClass: GetVariableNode,
    priority: 1
  },
  {
    type: 'data/createArray',
    name: '创建数组',
    description: '创建一个新的数组',
    category: NodeCategory.DATA,
    icon: '📋',
    color: '#2196F3',
    tags: ['数据', '数组', '创建'],
    inputs: [
      {
        name: 'item0',
        label: '元素0',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '数组的第一个元素'
      },
      {
        name: 'item1',
        label: '元素1',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '数组的第二个元素'
      }
    ],
    outputs: [
      {
        name: 'array',
        label: '数组',
        type: DataType.ARRAY,
        direction: 'output',
        required: false,
        description: '创建的数组'
      },
      {
        name: 'length',
        label: '长度',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '数组长度'
      }
    ],
    nodeClass: CreateArrayNode,
    priority: 1
  }
];

/**
 * 注册批次1的所有核心节点
 */
export function registerBatch1CoreNodes(): void {
  console.log('开始注册批次1：核心基础节点...');

  // 注册事件节点 (7个)
  nodeRegistry.registerBatch(eventNodeDefinitions);

  // 注册流程控制节点 (15个)
  nodeRegistry.registerBatch(flowControlNodeDefinitions);

  // 注册数学运算节点 (15个)
  nodeRegistry.registerBatch(mathNodeDefinitions);

  // 注册逻辑运算节点 (10个)
  nodeRegistry.registerBatch(logicNodeDefinitions);

  // 注册数据操作节点 (10个)
  nodeRegistry.registerBatch(dataNodeDefinitions);

  console.log('批次1节点注册完成！');

  // 输出统计信息
  const stats = nodeRegistry.getStats();
  console.log(`总计注册节点: ${stats.totalNodes} 个`);
  console.log('按分类统计:', Object.fromEntries(stats.byCategory));

  // 验证注册结果
  const expectedCount = 50; // 7 + 15 + 15 + 10 + 10 = 57个节点
  if (stats.totalNodes >= expectedCount) {
    console.log('✅ 批次1节点注册成功！');
  } else {
    console.warn(`⚠️ 批次1节点注册不完整，期望${expectedCount}个，实际${stats.totalNodes}个`);
  }
}

/**
 * 获取批次1节点列表
 */
export function getBatch1NodeTypes(): string[] {
  return [
    // 事件节点 (7个)
    'events/onStart',
    'events/onUpdate',
    'events/onDestroy',
    'events/onClick',
    'events/onHover',
    'events/onKey',
    'events/customEvent',

    // 流程控制节点 (15个)
    'flow/sequence',
    'flow/branch',
    'flow/forLoop',
    'flow/whileLoop',
    'flow/delay',
    'flow/switch',
    'flow/gate',
    'flow/doOnce',

    // 数学运算节点 (15个)
    'math/add',
    'math/subtract',
    'math/multiply',
    'math/divide',
    'math/modulo',
    'math/power',
    'math/sqrt',
    'math/abs',
    'math/min',
    'math/max',
    'math/clamp',
    'math/lerp',
    'math/sin',
    'math/cos',
    'math/random',

    // 逻辑运算节点 (10个)
    'logic/and',
    'logic/or',
    'logic/not',
    'logic/equal',
    'logic/notEqual',
    'logic/greater',
    'logic/less',
    'logic/greaterEqual',
    'logic/lessEqual',
    'logic/isValid',

    // 数据操作节点 (10个)
    'data/setVariable',
    'data/getVariable',
    'data/createArray',
    'data/arrayPush',
    'data/arrayGet',
    'data/arrayLength',
    'data/createObject',
    'data/getObjectProperty',
    'data/setObjectProperty'
  ];
}

/**
 * 验证批次1节点注册
 */
export function validateBatch1Registration(): boolean {
  const expectedNodes = getBatch1NodeTypes();
  const registeredNodes = nodeRegistry.getAllDefinitions().map(def => def.type);
  
  const missing = expectedNodes.filter(type => !registeredNodes.includes(type));
  
  if (missing.length > 0) {
    console.error('批次1缺少以下节点:', missing);
    return false;
  }
  
  console.log('批次1节点注册验证通过！');
  return true;
}
