/**
 * 属性编辑器组件
 * 用于编辑选中节点的属性
 */

import React, { useState, useCallback, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Select, 
  ColorPicker, 
  Button,
  Divider,
  Empty,
  Tooltip,
  Space
} from 'antd';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { IVisualScriptNode, NodeProperty } from '../core/types';
import './PropertyEditor.css';

const { Option } = Select;
const { TextArea } = Input;

interface PropertyEditorProps {
  /** 选中的节点 */
  selectedNode?: IVisualScriptNode;
  /** 属性变化回调 */
  onPropertyChange?: (property: string, value: any) => void;
  /** 是否只读模式 */
  readonly?: boolean;
  /** 编辑器宽度 */
  width?: number;
}

interface PropertyFieldProps {
  property: NodeProperty;
  value: any;
  onChange: (value: any) => void;
  readonly?: boolean;
}

/**
 * 属性字段组件
 */
const PropertyField: React.FC<PropertyFieldProps> = ({
  property,
  value,
  onChange,
  readonly = false
}) => {
  const handleChange = useCallback((newValue: any) => {
    if (!readonly) {
      onChange(newValue);
    }
  }, [onChange, readonly]);

  const renderField = () => {
    switch (property.type) {
      case 'string':
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={property.defaultValue?.toString() || ''}
            disabled={readonly}
          />
        );

      case 'number':
        return (
          <InputNumber
            value={value}
            onChange={handleChange}
            min={property.min}
            max={property.max}
            step={property.step || 1}
            placeholder={property.defaultValue?.toString() || '0'}
            disabled={readonly}
            style={{ width: '100%' }}
          />
        );

      case 'boolean':
        return (
          <Switch
            checked={Boolean(value)}
            onChange={handleChange}
            disabled={readonly}
          />
        );

      case 'select':
        return (
          <Select
            value={value}
            onChange={handleChange}
            placeholder="请选择..."
            disabled={readonly}
            style={{ width: '100%' }}
          >
            {property.options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'color':
        return (
          <ColorPicker
            value={value || '#ffffff'}
            onChange={(color) => handleChange(color.toHexString())}
            disabled={readonly}
            showText
            style={{ width: '100%' }}
          />
        );

      case 'vector':
        const vectorValue = value || { x: 0, y: 0, z: 0 };
        return (
          <Space.Compact style={{ width: '100%' }}>
            <InputNumber
              addonBefore="X"
              value={vectorValue.x}
              onChange={(x) => handleChange({ ...vectorValue, x: x || 0 })}
              disabled={readonly}
              style={{ width: '33.33%' }}
            />
            <InputNumber
              addonBefore="Y"
              value={vectorValue.y}
              onChange={(y) => handleChange({ ...vectorValue, y: y || 0 })}
              disabled={readonly}
              style={{ width: '33.33%' }}
            />
            <InputNumber
              addonBefore="Z"
              value={vectorValue.z}
              onChange={(z) => handleChange({ ...vectorValue, z: z || 0 })}
              disabled={readonly}
              style={{ width: '33.33%' }}
            />
          </Space.Compact>
        );

      case 'file':
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder="文件路径..."
            disabled={readonly}
            addonAfter={
              <Button 
                size="small" 
                disabled={readonly}
                onClick={() => {
                  // 这里应该打开文件选择对话框
                  console.log('打开文件选择器');
                }}
              >
                浏览
              </Button>
            }
          />
        );

      default:
        return (
          <TextArea
            value={value?.toString() || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={property.defaultValue?.toString() || ''}
            disabled={readonly}
            rows={2}
          />
        );
    }
  };

  return (
    <Form.Item
      label={
        <Space>
          {property.label}
          {property.description && (
            <Tooltip title={property.description}>
              <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
            </Tooltip>
          )}
        </Space>
      }
      className={`property-field ${property.readonly ? 'readonly' : ''}`}
    >
      {renderField()}
    </Form.Item>
  );
};

/**
 * 属性编辑器主组件
 */
export const PropertyEditor: React.FC<PropertyEditorProps> = ({
  selectedNode,
  onPropertyChange,
  readonly = false,
  width = 300
}) => {
  const [form] = Form.useForm();
  const [properties, setProperties] = useState<NodeProperty[]>([]);

  // 更新属性列表
  useEffect(() => {
    if (selectedNode) {
      const nodeProperties = selectedNode.getEditableProperties();
      setProperties(nodeProperties);
      
      // 设置表单初始值
      const initialValues: Record<string, any> = {};
      for (const prop of nodeProperties) {
        initialValues[prop.name] = prop.value;
      }
      form.setFieldsValue(initialValues);
    } else {
      setProperties([]);
      form.resetFields();
    }
  }, [selectedNode, form]);

  // 处理属性变化
  const handlePropertyChange = useCallback((property: string, value: any) => {
    if (!selectedNode || readonly) return;

    // 更新节点属性
    selectedNode.setProperty(property, value);
    
    // 更新本地状态
    setProperties(prev => prev.map(prop => 
      prop.name === property ? { ...prop, value } : prop
    ));

    // 通知外部
    onPropertyChange?.(property, value);
  }, [selectedNode, readonly, onPropertyChange]);

  // 重置属性
  const handleReset = useCallback(() => {
    if (!selectedNode || readonly) return;

    const defaultValues: Record<string, any> = {};
    for (const prop of properties) {
      const defaultValue = prop.defaultValue;
      selectedNode.setProperty(prop.name, defaultValue);
      defaultValues[prop.name] = defaultValue;
    }
    
    form.setFieldsValue(defaultValues);
    setProperties(prev => prev.map(prop => ({ 
      ...prop, 
      value: prop.defaultValue 
    })));
  }, [selectedNode, readonly, properties, form]);

  // 没有选中节点时的空状态
  if (!selectedNode) {
    return (
      <div className="property-editor" style={{ width }}>
        <Card title="属性编辑器" size="small">
          <Empty
            description="请选择一个节点"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </Card>
      </div>
    );
  }

  return (
    <div className="property-editor" style={{ width }}>
      <Card 
        title={
          <div className="editor-header">
            <span>属性编辑器</span>
            {!readonly && (
              <Tooltip title="重置为默认值">
                <Button
                  type="text"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                />
              </Tooltip>
            )}
          </div>
        }
        size="small"
        className="property-card"
      >
        {/* 节点基本信息 */}
        <div className="node-info">
          <div className="info-item">
            <span className="info-label">节点类型:</span>
            <span className="info-value">{selectedNode.type}</span>
          </div>
          <div className="info-item">
            <span className="info-label">节点ID:</span>
            <span className="info-value">{selectedNode.id}</span>
          </div>
          <div className="info-item">
            <span className="info-label">分类:</span>
            <span className="info-value">{selectedNode.category}</span>
          </div>
        </div>

        <Divider />

        {/* 属性表单 */}
        <Form
          form={form}
          layout="vertical"
          size="small"
          className="properties-form"
        >
          {properties.length === 0 ? (
            <Empty
              description="此节点没有可编辑的属性"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            properties.map((property) => (
              <PropertyField
                key={property.name}
                property={property}
                value={property.value}
                onChange={(value) => handlePropertyChange(property.name, value)}
                readonly={readonly || property.readonly}
              />
            ))
          )}
        </Form>

        {/* 端口信息 */}
        <Divider />
        
        <div className="ports-info">
          <div className="ports-section">
            <h4>输入端口 ({selectedNode.getInputPorts().length})</h4>
            <div className="ports-list">
              {selectedNode.getInputPorts().map((port) => (
                <div key={port.name} className="port-item">
                  <span className="port-name">{port.label}</span>
                  <span className="port-type">{port.type}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="ports-section">
            <h4>输出端口 ({selectedNode.getOutputPorts().length})</h4>
            <div className="ports-list">
              {selectedNode.getOutputPorts().map((port) => (
                <div key={port.name} className="port-item">
                  <span className="port-name">{port.label}</span>
                  <span className="port-type">{port.type}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PropertyEditor;
