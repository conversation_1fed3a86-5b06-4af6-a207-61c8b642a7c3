/**
 * 视觉脚本执行引擎
 * 基于现有visualscript引擎的改进版本
 */

import { 
  ScriptGraph, 
  IVisualScriptNode, 
  IExecutionContext, 
  ExecutionResult, 
  DebugInfo, 
  PerformanceStats,
  ScriptEvent,
  DataType
} from './types';
import { EventEmitter } from '../../utils/EventEmitter';
import { Entity, World } from '../../ecs';

/**
 * 执行上下文实现
 */
class ExecutionContext implements IExecutionContext {
  private inputValues: Map<string, any> = new Map();
  private outputValues: Map<string, any> = new Map();
  private variables: Map<string, any> = new Map();
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();
  private timeInfo = { deltaTime: 0, totalTime: 0, frameCount: 0 };

  constructor(
    private entity: Entity | null,
    private world: World | null,
    private engine: VisualScriptEngine
  ) {}

  getInputValue(portName: string): any {
    return this.inputValues.get(portName);
  }

  setOutputValue(portName: string, value: any): void {
    this.outputValues.set(portName, value);
  }

  getVariable(name: string): any {
    return this.variables.get(name);
  }

  setVariable(name: string, value: any): void {
    this.variables.set(name, value);
    this.engine.emit('variableChanged', { name, value });
  }

  getEntity(): Entity | null {
    return this.entity;
  }

  getWorld(): World | null {
    return this.world;
  }

  getService<T>(serviceName: string): T | null {
    // 从世界或引擎获取服务
    if (this.world) {
      return this.world.getService(serviceName) as T;
    }
    return null;
  }

  triggerEvent(eventName: string, data?: any): void {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          console.error(`事件监听器错误 ${eventName}:`, error);
        }
      }
    }
    this.engine.emit('eventTriggered', { eventName, data });
  }

  addEventListener(eventName: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, new Set());
    }
    this.eventListeners.get(eventName)!.add(callback);
  }

  removeEventListener(eventName: string, callback: (data: any) => void): void {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  getTime(): { deltaTime: number; totalTime: number; frameCount: number } {
    return { ...this.timeInfo };
  }

  log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    const logMethod = console[level] || console.log;
    logMethod(`[VisualScript] ${message}`, ...args);
    this.engine.emit('log', { level, message, args });
  }

  // 内部方法
  setInputValue(portName: string, value: any): void {
    this.inputValues.set(portName, value);
  }

  getOutputValue(portName: string): any {
    return this.outputValues.get(portName);
  }

  clearOutputs(): void {
    this.outputValues.clear();
  }

  updateTime(deltaTime: number, totalTime: number, frameCount: number): void {
    this.timeInfo = { deltaTime, totalTime, frameCount };
  }

  setVariables(variables: Map<string, any>): void {
    this.variables = new Map(variables);
  }
}

/**
 * 视觉脚本执行引擎
 */
export class VisualScriptEngine extends EventEmitter {
  private graph: ScriptGraph | null = null;
  private context: ExecutionContext;
  private running: boolean = false;
  private paused: boolean = false;
  private debugMode: boolean = false;
  private debugInfo: Map<string, DebugInfo> = new Map();
  private performanceStats: PerformanceStats;
  private executionQueue: string[] = [];
  private executedNodes: Set<string> = new Set();

  constructor(
    private entity: Entity | null = null,
    private world: World | null = null
  ) {
    super();
    this.context = new ExecutionContext(entity, world, this);
    this.performanceStats = this.initializePerformanceStats();
  }

  /**
   * 加载脚本图
   */
  public loadGraph(graph: ScriptGraph): void {
    this.graph = graph;
    this.context.setVariables(new Map(
      Array.from(graph.variables.entries()).map(([key, variable]) => [key, variable.value])
    ));
    this.emit('graphLoaded', graph);
  }

  /**
   * 执行脚本
   */
  public async execute(): Promise<ExecutionResult> {
    if (!this.graph) {
      throw new Error('没有加载脚本图');
    }

    if (this.running) {
      throw new Error('脚本正在运行中');
    }

    const startTime = performance.now();
    this.running = true;
    this.paused = false;
    this.executedNodes.clear();
    this.debugInfo.clear();

    try {
      this.emit('executionStarted');

      // 查找入口节点（事件节点）
      const entryNodes = this.findEntryNodes();
      if (entryNodes.length === 0) {
        throw new Error('没有找到入口节点');
      }

      // 执行入口节点
      for (const nodeId of entryNodes) {
        await this.executeNode(nodeId);
      }

      const executionTime = performance.now() - startTime;
      const result: ExecutionResult = {
        success: true,
        executionTime,
        nodesExecuted: this.executedNodes.size
      };

      this.emit('executionCompleted', result);
      return result;

    } catch (error) {
      const executionTime = performance.now() - startTime;
      const result: ExecutionResult = {
        success: false,
        error: error.message,
        executionTime,
        nodesExecuted: this.executedNodes.size
      };

      this.emit('executionError', { error, result });
      return result;

    } finally {
      this.running = false;
    }
  }

  /**
   * 停止执行
   */
  public stop(): void {
    this.running = false;
    this.paused = false;
    this.emit('executionStopped');
  }

  /**
   * 暂停执行
   */
  public pause(): void {
    this.paused = true;
    this.emit('executionPaused');
  }

  /**
   * 恢复执行
   */
  public resume(): void {
    this.paused = false;
    this.emit('executionResumed');
  }

  /**
   * 单步执行
   */
  public async step(): Promise<void> {
    if (!this.running || this.executionQueue.length === 0) {
      return;
    }

    const nodeId = this.executionQueue.shift()!;
    await this.executeNode(nodeId);
  }

  /**
   * 设置调试模式
   */
  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
    this.emit('debugModeChanged', enabled);
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(): Map<string, DebugInfo> {
    return new Map(this.debugInfo);
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): PerformanceStats {
    return { ...this.performanceStats };
  }

  /**
   * 更新时间信息
   */
  public updateTime(deltaTime: number, totalTime: number, frameCount: number): void {
    this.context.updateTime(deltaTime, totalTime, frameCount);
    this.performanceStats.frameStats.fps = 1000 / deltaTime;
    this.performanceStats.frameStats.frameTime = deltaTime;
  }

  /**
   * 触发事件
   */
  public triggerEvent(eventName: string, data?: any): void {
    this.context.triggerEvent(eventName, data);
  }

  /**
   * 查找入口节点
   */
  private findEntryNodes(): string[] {
    if (!this.graph) return [];

    const entryNodes: string[] = [];
    for (const [nodeId, node] of this.graph.nodes) {
      // 检查是否为事件节点或没有输入连接的节点
      if (this.isEntryNode(node)) {
        entryNodes.push(nodeId);
      }
    }
    return entryNodes;
  }

  /**
   * 判断是否为入口节点
   */
  private isEntryNode(node: IVisualScriptNode): boolean {
    // 事件节点通常没有触发输入端口，或者有特殊的事件输入
    const inputPorts = node.getInputPorts();
    const triggerInputs = inputPorts.filter(port => port.type === DataType.TRIGGER);
    
    // 如果没有触发输入，或者是特殊的事件节点，则认为是入口节点
    return triggerInputs.length === 0 || node.type.includes('Event') || node.type.includes('OnStart');
  }

  /**
   * 执行单个节点
   */
  private async executeNode(nodeId: string): Promise<void> {
    if (!this.graph || !this.running) return;

    const node = this.graph.nodes.get(nodeId);
    if (!node || this.executedNodes.has(nodeId)) return;

    // 检查暂停状态
    while (this.paused && this.running) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    if (!this.running) return;

    const startTime = performance.now();
    let debugInfo: DebugInfo = {
      nodeId,
      status: 'running',
      startTime,
      inputs: {},
      outputs: {}
    };

    try {
      // 准备输入数据
      await this.prepareNodeInputs(node);

      // 记录输入数据
      if (this.debugMode) {
        for (const port of node.getInputPorts()) {
          debugInfo.inputs[port.name] = this.context.getInputValue(port.name);
        }
      }

      // 执行节点
      this.emit('nodeExecutionStarted', { nodeId, node });
      await node.execute(this.context);

      // 记录输出数据
      if (this.debugMode) {
        for (const port of node.getOutputPorts()) {
          debugInfo.outputs[port.name] = this.context.getOutputValue(port.name);
        }
      }

      // 传播输出到连接的节点
      await this.propagateOutputs(node);

      // 标记为已执行
      this.executedNodes.add(nodeId);

      // 更新调试信息
      debugInfo.status = 'completed';
      debugInfo.endTime = performance.now();

      // 更新性能统计
      this.updateNodeStats(node.type, debugInfo.endTime - debugInfo.startTime);

      this.emit('nodeExecutionCompleted', { nodeId, node, debugInfo });

    } catch (error) {
      debugInfo.status = 'error';
      debugInfo.error = error.message;
      debugInfo.endTime = performance.now();

      this.emit('nodeExecutionError', { nodeId, node, error, debugInfo });
      throw error;

    } finally {
      if (this.debugMode) {
        this.debugInfo.set(nodeId, debugInfo);
      }
      this.context.clearOutputs();
    }
  }

  /**
   * 准备节点输入数据
   */
  private async prepareNodeInputs(node: IVisualScriptNode): Promise<void> {
    if (!this.graph) return;

    // 清除之前的输入
    for (const port of node.getInputPorts()) {
      this.context.setInputValue(port.name, port.defaultValue);
    }

    // 从连接获取输入数据
    for (const connection of this.graph.connections.values()) {
      if (connection.toNodeId === node.id) {
        const sourceNode = this.graph.nodes.get(connection.fromNodeId);
        if (sourceNode && this.executedNodes.has(connection.fromNodeId)) {
          // 获取源节点的输出值
          const outputValue = this.context.getOutputValue(connection.fromPort);
          this.context.setInputValue(connection.toPort, outputValue);
        }
      }
    }
  }

  /**
   * 传播输出到连接的节点
   */
  private async propagateOutputs(node: IVisualScriptNode): Promise<void> {
    if (!this.graph) return;

    // 查找所有从此节点输出的连接
    const outputConnections = Array.from(this.graph.connections.values())
      .filter(conn => conn.fromNodeId === node.id);

    // 执行连接的目标节点
    const targetNodes = new Set<string>();
    for (const connection of outputConnections) {
      targetNodes.add(connection.toNodeId);
    }

    // 递归执行目标节点
    for (const targetNodeId of targetNodes) {
      if (!this.executedNodes.has(targetNodeId)) {
        await this.executeNode(targetNodeId);
      }
    }
  }

  /**
   * 初始化性能统计
   */
  private initializePerformanceStats(): PerformanceStats {
    return {
      totalExecutionTime: 0,
      nodeStats: new Map(),
      memoryUsage: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0
      },
      frameStats: {
        fps: 0,
        frameTime: 0,
        minFrameTime: Infinity,
        maxFrameTime: 0
      }
    };
  }

  /**
   * 更新节点统计
   */
  private updateNodeStats(nodeType: string, executionTime: number): void {
    const stats = this.performanceStats.nodeStats.get(nodeType) || {
      count: 0,
      totalTime: 0,
      averageTime: 0,
      minTime: Infinity,
      maxTime: 0
    };

    stats.count++;
    stats.totalTime += executionTime;
    stats.averageTime = stats.totalTime / stats.count;
    stats.minTime = Math.min(stats.minTime, executionTime);
    stats.maxTime = Math.max(stats.maxTime, executionTime);

    this.performanceStats.nodeStats.set(nodeType, stats);
  }
}
