/**
 * 统一的节点注册系统
 * 简化的注册机制，避免复杂的批次管理
 */

import { 
  NodeDefinition, 
  NodeCategory, 
  IVisualScriptNode,
  ValidationResult 
} from '../../core/types';
import { EventEmitter } from '../../../utils/EventEmitter';

/**
 * 节点搜索选项
 */
export interface NodeSearchOptions {
  /** 搜索关键词 */
  query?: string;
  /** 分类过滤 */
  category?: NodeCategory;
  /** 标签过滤 */
  tags?: string[];
  /** 是否启用模糊搜索 */
  fuzzy?: boolean;
  /** 最大结果数量 */
  limit?: number;
}

/**
 * 节点统计信息
 */
export interface NodeStats {
  /** 总节点数 */
  totalNodes: number;
  /** 按分类统计 */
  byCategory: Map<NodeCategory, number>;
  /** 按标签统计 */
  byTags: Map<string, number>;
  /** 最近注册的节点 */
  recentlyRegistered: string[];
  /** 最常用的节点 */
  mostUsed: string[];
}

/**
 * 节点注册表
 */
export class NodeRegistry extends EventEmitter {
  /** 单例实例 */
  private static instance: NodeRegistry | null = null;
  
  /** 节点定义映射 */
  private nodes: Map<string, NodeDefinition> = new Map();
  
  /** 分类索引 */
  private categoryIndex: Map<NodeCategory, Set<string>> = new Map();
  
  /** 标签索引 */
  private tagIndex: Map<string, Set<string>> = new Map();
  
  /** 节点使用统计 */
  private usageStats: Map<string, number> = new Map();
  
  /** 注册历史 */
  private registrationHistory: { type: string; timestamp: number }[] = [];

  private constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  /**
   * 注册节点
   */
  public register(definition: NodeDefinition): void {
    // 验证节点定义
    const validation = this.validateDefinition(definition);
    if (!validation.isValid) {
      throw new Error(`节点注册失败: ${validation.errors.join(', ')}`);
    }

    // 检查是否已存在
    if (this.nodes.has(definition.type)) {
      console.warn(`节点类型 ${definition.type} 已存在，将被覆盖`);
    }

    // 注册节点
    this.nodes.set(definition.type, definition);

    // 更新分类索引
    this.updateCategoryIndex(definition);

    // 更新标签索引
    this.updateTagIndex(definition);

    // 记录注册历史
    this.registrationHistory.push({
      type: definition.type,
      timestamp: Date.now()
    });

    // 触发注册事件
    this.emit('nodeRegistered', definition);

    console.log(`节点已注册: ${definition.type} (${definition.name})`);
  }

  /**
   * 批量注册节点
   */
  public registerBatch(definitions: NodeDefinition[]): void {
    const errors: string[] = [];
    
    for (const definition of definitions) {
      try {
        this.register(definition);
      } catch (error) {
        errors.push(`${definition.type}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      console.error('批量注册中的错误:', errors);
    }

    this.emit('batchRegistered', { 
      total: definitions.length, 
      success: definitions.length - errors.length,
      errors 
    });
  }

  /**
   * 获取节点定义
   */
  public getDefinition(type: string): NodeDefinition | null {
    return this.nodes.get(type) || null;
  }

  /**
   * 创建节点实例
   */
  public createNode(type: string, id?: string): IVisualScriptNode | null {
    const definition = this.getDefinition(type);
    if (!definition) {
      console.error(`未找到节点类型: ${type}`);
      return null;
    }

    try {
      const node = new definition.nodeClass(id);
      
      // 更新使用统计
      const currentCount = this.usageStats.get(type) || 0;
      this.usageStats.set(type, currentCount + 1);
      
      // 触发创建事件
      this.emit('nodeCreated', { type, nodeId: node.id });
      
      return node;
    } catch (error) {
      console.error(`创建节点失败 ${type}:`, error);
      return null;
    }
  }

  /**
   * 获取所有节点定义
   */
  public getAllDefinitions(): NodeDefinition[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 按分类获取节点
   */
  public getByCategory(category: NodeCategory): NodeDefinition[] {
    const types = this.categoryIndex.get(category) || new Set();
    return Array.from(types).map(type => this.nodes.get(type)!).filter(Boolean);
  }

  /**
   * 按标签获取节点
   */
  public getByTag(tag: string): NodeDefinition[] {
    const types = this.tagIndex.get(tag) || new Set();
    return Array.from(types).map(type => this.nodes.get(type)!).filter(Boolean);
  }

  /**
   * 搜索节点
   */
  public search(options: NodeSearchOptions): NodeDefinition[] {
    let results = this.getAllDefinitions();

    // 分类过滤
    if (options.category) {
      results = results.filter(def => def.category === options.category);
    }

    // 标签过滤
    if (options.tags && options.tags.length > 0) {
      results = results.filter(def => 
        def.tags && def.tags.some(tag => options.tags!.includes(tag))
      );
    }

    // 关键词搜索
    if (options.query) {
      const query = options.query.toLowerCase();
      results = results.filter(def => {
        const searchText = `${def.name} ${def.description} ${def.type} ${(def.tags || []).join(' ')}`.toLowerCase();
        
        if (options.fuzzy) {
          // 模糊搜索
          return this.fuzzyMatch(searchText, query);
        } else {
          // 精确搜索
          return searchText.includes(query);
        }
      });
    }

    // 按使用频率排序
    results.sort((a, b) => {
      const usageA = this.usageStats.get(a.type) || 0;
      const usageB = this.usageStats.get(b.type) || 0;
      return usageB - usageA;
    });

    // 限制结果数量
    if (options.limit && options.limit > 0) {
      results = results.slice(0, options.limit);
    }

    return results;
  }

  /**
   * 获取所有分类
   */
  public getCategories(): NodeCategory[] {
    return Array.from(this.categoryIndex.keys());
  }

  /**
   * 获取所有标签
   */
  public getTags(): string[] {
    return Array.from(this.tagIndex.keys());
  }

  /**
   * 获取统计信息
   */
  public getStats(): NodeStats {
    const byCategory = new Map<NodeCategory, number>();
    const byTags = new Map<string, number>();

    for (const definition of this.nodes.values()) {
      // 统计分类
      const categoryCount = byCategory.get(definition.category) || 0;
      byCategory.set(definition.category, categoryCount + 1);

      // 统计标签
      if (definition.tags) {
        for (const tag of definition.tags) {
          const tagCount = byTags.get(tag) || 0;
          byTags.set(tag, tagCount + 1);
        }
      }
    }

    // 最近注册的节点（最近10个）
    const recentlyRegistered = this.registrationHistory
      .slice(-10)
      .reverse()
      .map(item => item.type);

    // 最常用的节点（前10个）
    const mostUsed = Array.from(this.usageStats.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(item => item[0]);

    return {
      totalNodes: this.nodes.size,
      byCategory,
      byTags,
      recentlyRegistered,
      mostUsed
    };
  }

  /**
   * 验证注册表完整性
   */
  public validate(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 检查每个节点定义
    for (const [type, definition] of this.nodes.entries()) {
      const validation = this.validateDefinition(definition);
      if (!validation.isValid) {
        errors.push(`节点 ${type}: ${validation.errors.join(', ')}`);
      }
      warnings.push(...validation.warnings.map(w => `节点 ${type}: ${w}`));
      suggestions.push(...validation.suggestions.map(s => `节点 ${type}: ${s}`));
    }

    // 检查分类覆盖
    const missingCategories = Object.values(NodeCategory).filter(
      category => !this.categoryIndex.has(category)
    );
    if (missingCategories.length > 0) {
      warnings.push(`缺少以下分类的节点: ${missingCategories.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 清空注册表
   */
  public clear(): void {
    this.nodes.clear();
    this.categoryIndex.clear();
    this.tagIndex.clear();
    this.usageStats.clear();
    this.registrationHistory = [];
    this.emit('registryCleared');
  }

  /**
   * 验证节点定义
   */
  private validateDefinition(definition: NodeDefinition): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 必需字段检查
    if (!definition.type) errors.push('节点类型不能为空');
    if (!definition.name) errors.push('节点名称不能为空');
    if (!definition.category) errors.push('节点分类不能为空');
    if (!definition.nodeClass) errors.push('节点类不能为空');

    // 类型格式检查
    if (definition.type && !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(definition.type)) {
      errors.push('节点类型格式无效，应该以字母开头，只包含字母、数字和下划线');
    }

    // 端口检查
    if (!definition.inputs) definition.inputs = [];
    if (!definition.outputs) definition.outputs = [];

    // 检查端口名称唯一性
    const inputNames = new Set();
    const outputNames = new Set();
    
    for (const input of definition.inputs) {
      if (inputNames.has(input.name)) {
        errors.push(`重复的输入端口名称: ${input.name}`);
      }
      inputNames.add(input.name);
    }
    
    for (const output of definition.outputs) {
      if (outputNames.has(output.name)) {
        errors.push(`重复的输出端口名称: ${output.name}`);
      }
      outputNames.add(output.name);
    }

    // 建议
    if (!definition.description) {
      suggestions.push('建议添加节点描述');
    }
    if (!definition.icon) {
      suggestions.push('建议添加节点图标');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 更新分类索引
   */
  private updateCategoryIndex(definition: NodeDefinition): void {
    if (!this.categoryIndex.has(definition.category)) {
      this.categoryIndex.set(definition.category, new Set());
    }
    this.categoryIndex.get(definition.category)!.add(definition.type);
  }

  /**
   * 更新标签索引
   */
  private updateTagIndex(definition: NodeDefinition): void {
    if (definition.tags) {
      for (const tag of definition.tags) {
        if (!this.tagIndex.has(tag)) {
          this.tagIndex.set(tag, new Set());
        }
        this.tagIndex.get(tag)!.add(definition.type);
      }
    }
  }

  /**
   * 模糊匹配
   */
  private fuzzyMatch(text: string, query: string): boolean {
    const textChars = text.split('');
    const queryChars = query.split('');
    let queryIndex = 0;

    for (let i = 0; i < textChars.length && queryIndex < queryChars.length; i++) {
      if (textChars[i] === queryChars[queryIndex]) {
        queryIndex++;
      }
    }

    return queryIndex === queryChars.length;
  }
}

// 导出单例实例
export const nodeRegistry = NodeRegistry.getInstance();
