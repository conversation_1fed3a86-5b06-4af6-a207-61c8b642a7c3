/**
 * 视觉脚本节点基类
 * 统一的节点实现基础，所有节点都应该继承此类
 */

import { 
  IVisualScriptNode, 
  IExecutionContext, 
  NodePort, 
  NodeProperty, 
  NodeCategory,
  DataType,
  ValidationResult
} from '../../core/types';
import { generateId } from '../../../utils/IdGenerator';

/**
 * 节点基类
 */
export abstract class BaseNode implements IVisualScriptNode {
  /** 节点ID */
  public readonly id: string;
  
  /** 节点类型 */
  public abstract readonly type: string;
  
  /** 节点名称 */
  public name: string;
  
  /** 节点分类 */
  public abstract readonly category: NodeCategory;
  
  /** 节点位置 */
  public position: { x: number; y: number } = { x: 0, y: 0 };
  
  /** 是否启用 */
  public enabled: boolean = true;
  
  /** 输入端口定义 */
  protected inputPorts: Map<string, NodePort> = new Map();
  
  /** 输出端口定义 */
  protected outputPorts: Map<string, NodePort> = new Map();
  
  /** 节点属性 */
  protected properties: Map<string, NodeProperty> = new Map();
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  constructor(id?: string) {
    this.id = id || generateId();
    this.name = this.getDefaultName();
    this.initializePorts();
    this.initializeProperties();
    this.initialized = true;
  }

  /**
   * 获取默认节点名称
   */
  protected abstract getDefaultName(): string;

  /**
   * 初始化端口定义
   * 子类应该重写此方法来定义输入输出端口
   */
  protected abstract initializePorts(): void;

  /**
   * 初始化属性定义
   * 子类可以重写此方法来定义可编辑属性
   */
  protected initializeProperties(): void {
    // 默认属性：节点名称
    this.addProperty({
      name: 'name',
      label: '节点名称',
      type: 'string',
      value: this.name,
      description: '节点的显示名称'
    });

    // 默认属性：是否启用
    this.addProperty({
      name: 'enabled',
      label: '启用',
      type: 'boolean',
      value: this.enabled,
      description: '是否启用此节点'
    });
  }

  /**
   * 添加输入端口
   */
  protected addInputPort(port: Omit<NodePort, 'direction'>): void {
    const fullPort: NodePort = { ...port, direction: 'input' };
    this.inputPorts.set(port.name, fullPort);
  }

  /**
   * 添加输出端口
   */
  protected addOutputPort(port: Omit<NodePort, 'direction'>): void {
    const fullPort: NodePort = { ...port, direction: 'output' };
    this.outputPorts.set(port.name, fullPort);
  }

  /**
   * 添加属性
   */
  protected addProperty(property: NodeProperty): void {
    this.properties.set(property.name, property);
  }

  /**
   * 获取输入端口列表
   */
  public getInputPorts(): NodePort[] {
    return Array.from(this.inputPorts.values());
  }

  /**
   * 获取输出端口列表
   */
  public getOutputPorts(): NodePort[] {
    return Array.from(this.outputPorts.values());
  }

  /**
   * 获取指定输入端口
   */
  public getInputPort(name: string): NodePort | null {
    return this.inputPorts.get(name) || null;
  }

  /**
   * 获取指定输出端口
   */
  public getOutputPort(name: string): NodePort | null {
    return this.outputPorts.get(name) || null;
  }

  /**
   * 执行节点
   * 子类必须实现此方法
   */
  public abstract execute(context: IExecutionContext): Promise<void>;

  /**
   * 验证节点
   */
  public validate(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 检查必需的输入端口
    for (const port of this.inputPorts.values()) {
      if (port.required && port.defaultValue === undefined) {
        // 这里应该检查是否有连接，但需要图形上下文
        // 暂时跳过，在图形验证时处理
      }
    }

    // 检查节点名称
    if (!this.name || this.name.trim() === '') {
      errors.push('节点名称不能为空');
    }

    // 检查节点是否正确初始化
    if (!this.initialized) {
      errors.push('节点未正确初始化');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 序列化节点
   */
  public serialize(): any {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      category: this.category,
      position: { ...this.position },
      enabled: this.enabled,
      properties: Object.fromEntries(
        Array.from(this.properties.entries()).map(([key, prop]) => [
          key,
          { ...prop }
        ])
      )
    };
  }

  /**
   * 反序列化节点
   */
  public deserialize(data: any): void {
    if (data.name) this.name = data.name;
    if (data.position) this.position = { ...data.position };
    if (data.enabled !== undefined) this.enabled = data.enabled;
    
    if (data.properties) {
      for (const [key, propData] of Object.entries(data.properties)) {
        const prop = this.properties.get(key);
        if (prop) {
          prop.value = (propData as any).value;
        }
      }
    }
  }

  /**
   * 克隆节点
   */
  public clone(): IVisualScriptNode {
    const NodeClass = this.constructor as new (id?: string) => IVisualScriptNode;
    const cloned = new NodeClass();
    const serialized = this.serialize();
    delete serialized.id; // 让克隆节点生成新ID
    cloned.deserialize(serialized);
    return cloned;
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.inputPorts.clear();
    this.outputPorts.clear();
    this.properties.clear();
  }

  /**
   * 获取可编辑属性
   */
  public getEditableProperties(): NodeProperty[] {
    return Array.from(this.properties.values()).filter(prop => !prop.readonly);
  }

  /**
   * 设置属性值
   */
  public setProperty(name: string, value: any): void {
    const property = this.properties.get(name);
    if (property && !property.readonly) {
      property.value = value;
      
      // 处理特殊属性
      if (name === 'name') {
        this.name = value;
      } else if (name === 'enabled') {
        this.enabled = value;
      }
      
      this.onPropertyChanged(name, value);
    }
  }

  /**
   * 获取属性值
   */
  public getProperty(name: string): any {
    const property = this.properties.get(name);
    return property ? property.value : undefined;
  }

  /**
   * 属性变化回调
   * 子类可以重写此方法来响应属性变化
   */
  protected onPropertyChanged(name: string, value: any): void {
    // 默认实现为空
  }

  /**
   * 获取输入值的辅助方法
   */
  protected getInput<T = any>(context: IExecutionContext, portName: string, defaultValue?: T): T {
    const value = context.getInputValue(portName);
    return value !== undefined ? value : defaultValue;
  }

  /**
   * 设置输出值的辅助方法
   */
  protected setOutput(context: IExecutionContext, portName: string, value: any): void {
    context.setOutputValue(portName, value);
  }

  /**
   * 日志输出的辅助方法
   */
  protected log(context: IExecutionContext, level: 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    context.log(level, `[${this.type}:${this.name}] ${message}`, ...args);
  }

  /**
   * 触发输出事件的辅助方法
   */
  protected triggerOutput(context: IExecutionContext, portName: string): void {
    this.setOutput(context, portName, true);
  }

  /**
   * 检查输入是否已连接的辅助方法
   */
  protected hasInput(context: IExecutionContext, portName: string): boolean {
    return context.getInputValue(portName) !== undefined;
  }

  /**
   * 等待指定时间的辅助方法
   */
  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证输入类型的辅助方法
   */
  protected validateInputType(value: any, expectedType: DataType): boolean {
    switch (expectedType) {
      case DataType.BOOLEAN:
        return typeof value === 'boolean';
      case DataType.NUMBER:
        return typeof value === 'number' && !isNaN(value);
      case DataType.STRING:
        return typeof value === 'string';
      case DataType.OBJECT:
        return typeof value === 'object' && value !== null;
      case DataType.ARRAY:
        return Array.isArray(value);
      case DataType.TRIGGER:
        return value === true || value === false;
      default:
        return true; // ANY类型或未知类型
    }
  }

  /**
   * 转换输入值类型的辅助方法
   */
  protected convertInputValue(value: any, targetType: DataType): any {
    if (value === undefined || value === null) {
      return value;
    }

    switch (targetType) {
      case DataType.BOOLEAN:
        return Boolean(value);
      case DataType.NUMBER:
        return Number(value);
      case DataType.STRING:
        return String(value);
      case DataType.ARRAY:
        return Array.isArray(value) ? value : [value];
      default:
        return value;
    }
  }
}
