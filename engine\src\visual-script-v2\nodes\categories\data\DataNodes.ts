/**
 * 数据操作节点
 * 提供变量存储、数组操作、对象处理等数据功能
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 设置变量节点
 */
export class SetVariableNode extends BaseNode {
  public readonly type = 'data/setVariable';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '设置变量';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发设置变量'
    });

    this.addInputPort({
      name: 'value',
      label: '值',
      type: DataType.ANY,
      required: false,
      description: '要设置的值'
    });

    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '设置完成后触发'
    });

    this.addOutputPort({
      name: 'value',
      label: '输出值',
      type: DataType.ANY,
      required: false,
      description: '设置的值'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'variableName',
      label: '变量名',
      type: 'string',
      value: 'myVariable',
      defaultValue: 'myVariable',
      description: '变量的名称'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const variableName = this.getProperty('variableName');
      const value = this.getInput(context, 'value');

      context.setVariable(variableName, value);

      this.setOutput(context, 'value', value);
      this.triggerOutput(context, 'completed');

      this.log(context, 'info', `变量 ${variableName} 已设置为:`, value);
    }
  }
}

/**
 * 获取变量节点
 */
export class GetVariableNode extends BaseNode {
  public readonly type = 'data/getVariable';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '获取变量';
  }

  protected initializePorts(): void {
    this.addOutputPort({
      name: 'value',
      label: '值',
      type: DataType.ANY,
      required: false,
      description: '变量的值'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'variableName',
      label: '变量名',
      type: 'string',
      value: 'myVariable',
      defaultValue: 'myVariable',
      description: '要获取的变量名称'
    });

    this.addProperty({
      name: 'defaultValue',
      label: '默认值',
      type: 'string',
      value: '',
      defaultValue: '',
      description: '变量不存在时的默认值'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const variableName = this.getProperty('variableName');
    const defaultValue = this.getProperty('defaultValue');

    const value = context.getVariable(variableName) ?? defaultValue;
    this.setOutput(context, 'value', value);
  }
}

/**
 * 创建数组节点
 */
export class CreateArrayNode extends BaseNode {
  public readonly type = 'data/createArray';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '创建数组';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'item0',
      label: '元素0',
      type: DataType.ANY,
      required: false,
      description: '数组的第一个元素'
    });

    this.addInputPort({
      name: 'item1',
      label: '元素1',
      type: DataType.ANY,
      required: false,
      description: '数组的第二个元素'
    });

    this.addInputPort({
      name: 'item2',
      label: '元素2',
      type: DataType.ANY,
      required: false,
      description: '数组的第三个元素'
    });

    this.addOutputPort({
      name: 'array',
      label: '数组',
      type: DataType.ARRAY,
      required: false,
      description: '创建的数组'
    });

    this.addOutputPort({
      name: 'length',
      label: '长度',
      type: DataType.NUMBER,
      required: false,
      description: '数组长度'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const array: any[] = [];

    // 收集所有非undefined的输入
    for (let i = 0; i < 10; i++) {
      const value = this.getInput(context, `item${i}`);
      if (value !== undefined) {
        array.push(value);
      }
    }

    this.setOutput(context, 'array', array);
    this.setOutput(context, 'length', array.length);
  }
}

/**
 * 数组添加元素节点
 */
export class ArrayPushNode extends BaseNode {
  public readonly type = 'data/arrayPush';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数组添加';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发添加操作'
    });

    this.addInputPort({
      name: 'array',
      label: '数组',
      type: DataType.ARRAY,
      required: true,
      description: '目标数组'
    });

    this.addInputPort({
      name: 'item',
      label: '元素',
      type: DataType.ANY,
      required: false,
      description: '要添加的元素'
    });

    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '添加完成后触发'
    });

    this.addOutputPort({
      name: 'array',
      label: '数组',
      type: DataType.ARRAY,
      required: false,
      description: '修改后的数组'
    });

    this.addOutputPort({
      name: 'length',
      label: '新长度',
      type: DataType.NUMBER,
      required: false,
      description: '数组的新长度'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const array = this.getInput(context, 'array', []);
      const item = this.getInput(context, 'item');

      if (Array.isArray(array)) {
        array.push(item);
        this.setOutput(context, 'array', array);
        this.setOutput(context, 'length', array.length);
        this.triggerOutput(context, 'completed');
      } else {
        this.log(context, 'error', '输入不是有效的数组');
      }
    }
  }
}

/**
 * 数组获取元素节点
 */
export class ArrayGetNode extends BaseNode {
  public readonly type = 'data/arrayGet';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数组获取';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'array',
      label: '数组',
      type: DataType.ARRAY,
      required: true,
      description: '源数组'
    });

    this.addInputPort({
      name: 'index',
      label: '索引',
      type: DataType.NUMBER,
      required: true,
      defaultValue: 0,
      description: '元素索引'
    });

    this.addOutputPort({
      name: 'item',
      label: '元素',
      type: DataType.ANY,
      required: false,
      description: '获取的元素'
    });

    this.addOutputPort({
      name: 'exists',
      label: '存在',
      type: DataType.BOOLEAN,
      required: false,
      description: '索引是否存在'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const array = this.getInput(context, 'array', []);
    const index = this.getInput(context, 'index', 0);

    if (Array.isArray(array)) {
      const exists = index >= 0 && index < array.length;
      const item = exists ? array[index] : undefined;

      this.setOutput(context, 'item', item);
      this.setOutput(context, 'exists', exists);
    } else {
      this.log(context, 'error', '输入不是有效的数组');
      this.setOutput(context, 'exists', false);
    }
  }
}

/**
 * 数组长度节点
 */
export class ArrayLengthNode extends BaseNode {
  public readonly type = 'data/arrayLength';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数组长度';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'array',
      label: '数组',
      type: DataType.ARRAY,
      required: true,
      description: '要获取长度的数组'
    });

    this.addOutputPort({
      name: 'length',
      label: '长度',
      type: DataType.NUMBER,
      required: false,
      description: '数组长度'
    });

    this.addOutputPort({
      name: 'isEmpty',
      label: '为空',
      type: DataType.BOOLEAN,
      required: false,
      description: '数组是否为空'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const array = this.getInput(context, 'array', []);

    if (Array.isArray(array)) {
      const length = array.length;
      this.setOutput(context, 'length', length);
      this.setOutput(context, 'isEmpty', length === 0);
    } else {
      this.log(context, 'error', '输入不是有效的数组');
      this.setOutput(context, 'length', 0);
      this.setOutput(context, 'isEmpty', true);
    }
  }
}

/**
 * 创建对象节点
 */
export class CreateObjectNode extends BaseNode {
  public readonly type = 'data/createObject';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '创建对象';
  }

  protected initializePorts(): void {
    this.addOutputPort({
      name: 'object',
      label: '对象',
      type: DataType.OBJECT,
      required: false,
      description: '创建的对象'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'properties',
      label: '属性定义',
      type: 'string',
      value: 'name:string,age:number,active:boolean',
      defaultValue: 'name:string,age:number,active:boolean',
      description: '对象属性定义，格式：属性名:类型,属性名:类型'
    });
  }

  protected initializePorts(): void {
    super.initializePorts();
    this.updatePortsFromProperties();
  }

  protected onPropertyChanged(name: string, value: any): void {
    if (name === 'properties') {
      this.updatePortsFromProperties();
    }
  }

  private updatePortsFromProperties(): void {
    const properties = this.getProperty('properties') || '';
    const props = properties.split(',').map(p => p.trim()).filter(p => p);

    // 清除现有的输入端口（除了基础端口）
    this.inputPorts.clear();

    // 根据属性定义添加输入端口
    for (const prop of props) {
      const [propName, propType] = prop.split(':').map(s => s.trim());
      if (propName) {
        const dataType = this.getDataTypeFromString(propType);
        this.addInputPort({
          name: propName,
          label: propName,
          type: dataType,
          required: false,
          description: `对象的${propName}属性`
        });
      }
    }
  }

  private getDataTypeFromString(typeStr: string): DataType {
    switch (typeStr?.toLowerCase()) {
      case 'string': return DataType.STRING;
      case 'number': return DataType.NUMBER;
      case 'boolean': return DataType.BOOLEAN;
      case 'array': return DataType.ARRAY;
      case 'object': return DataType.OBJECT;
      default: return DataType.ANY;
    }
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const obj: any = {};

    // 从输入端口收集属性值
    for (const [portName, port] of this.inputPorts) {
      const value = this.getInput(context, portName);
      if (value !== undefined) {
        obj[portName] = value;
      }
    }

    this.setOutput(context, 'object', obj);
  }
}

/**
 * 获取对象属性节点
 */
export class GetObjectPropertyNode extends BaseNode {
  public readonly type = 'data/getObjectProperty';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '获取对象属性';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'object',
      label: '对象',
      type: DataType.OBJECT,
      required: true,
      description: '源对象'
    });

    this.addInputPort({
      name: 'property',
      label: '属性名',
      type: DataType.STRING,
      required: true,
      description: '要获取的属性名'
    });

    this.addOutputPort({
      name: 'value',
      label: '属性值',
      type: DataType.ANY,
      required: false,
      description: '属性的值'
    });

    this.addOutputPort({
      name: 'exists',
      label: '存在',
      type: DataType.BOOLEAN,
      required: false,
      description: '属性是否存在'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const obj = this.getInput(context, 'object', {});
    const property = this.getInput(context, 'property', '');

    if (typeof obj === 'object' && obj !== null) {
      const exists = property in obj;
      const value = exists ? obj[property] : undefined;

      this.setOutput(context, 'value', value);
      this.setOutput(context, 'exists', exists);
    } else {
      this.log(context, 'error', '输入不是有效的对象');
      this.setOutput(context, 'exists', false);
    }
  }
}

/**
 * 设置对象属性节点
 */
export class SetObjectPropertyNode extends BaseNode {
  public readonly type = 'data/setObjectProperty';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '设置对象属性';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发设置操作'
    });

    this.addInputPort({
      name: 'object',
      label: '对象',
      type: DataType.OBJECT,
      required: true,
      description: '目标对象'
    });

    this.addInputPort({
      name: 'property',
      label: '属性名',
      type: DataType.STRING,
      required: true,
      description: '要设置的属性名'
    });

    this.addInputPort({
      name: 'value',
      label: '属性值',
      type: DataType.ANY,
      required: false,
      description: '要设置的值'
    });

    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '设置完成后触发'
    });

    this.addOutputPort({
      name: 'object',
      label: '对象',
      type: DataType.OBJECT,
      required: false,
      description: '修改后的对象'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const obj = this.getInput(context, 'object', {});
      const property = this.getInput(context, 'property', '');
      const value = this.getInput(context, 'value');

      if (typeof obj === 'object' && obj !== null && property) {
        obj[property] = value;
        this.setOutput(context, 'object', obj);
        this.triggerOutput(context, 'completed');

        this.log(context, 'info', `对象属性 ${property} 已设置为:`, value);
      } else {
        this.log(context, 'error', '无效的对象或属性名');
      }
    }
  }
}