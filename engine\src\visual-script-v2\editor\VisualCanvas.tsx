/**
 * 视觉脚本画布组件
 * 基于React Flow实现的节点编辑画布
 */

import React, { useState, useCallback, useMemo, useRef } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Connection,
  NodeChange,
  EdgeChange,
  ReactFlowProvider,
  ReactFlowInstance,
  MarkerType
} from 'reactflow';
import { Button, message, Modal, Tooltip } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StopOutlined,
  SaveOutlined,
  FolderOpenOutlined,
  DeleteOutlined,
  CopyOutlined
} from '@ant-design/icons';

import { IVisualScriptNode, ScriptGraph, NodeConnection } from '../core/types';
import { nodeRegistry } from '../nodes/registry/NodeRegistry';
import { VisualScriptEngine } from '../core/Engine';
import { CustomNodeComponent } from './CustomNodeComponent';
import './VisualCanvas.css';

// 导入React Flow样式
import 'reactflow/dist/style.css';

interface VisualCanvasProps {
  /** 画布宽度 */
  width?: number;
  /** 画布高度 */
  height?: number;
  /** 脚本图数据 */
  graph?: ScriptGraph;
  /** 图形变化回调 */
  onGraphChange?: (graph: ScriptGraph) => void;
  /** 节点选择回调 */
  onNodeSelect?: (node: IVisualScriptNode | null) => void;
  /** 是否只读模式 */
  readonly?: boolean;
}

// 自定义节点类型映射
const nodeTypes = {
  visualScriptNode: CustomNodeComponent
};

/**
 * 视觉脚本画布主组件
 */
export const VisualCanvas: React.FC<VisualCanvasProps> = ({
  width = 800,
  height = 600,
  graph,
  onGraphChange,
  onNodeSelect,
  readonly = false
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const engineRef = useRef<VisualScriptEngine | null>(null);

  // 从ScriptGraph转换为React Flow格式
  const convertGraphToFlow = useCallback((scriptGraph: ScriptGraph) => {
    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];

    // 转换节点
    for (const [nodeId, scriptNode] of scriptGraph.nodes) {
      flowNodes.push({
        id: nodeId,
        type: 'visualScriptNode',
        position: scriptNode.position,
        data: {
          scriptNode,
          onSelect: () => onNodeSelect?.(scriptNode),
          onDelete: () => handleDeleteNode(nodeId),
          readonly
        },
        selected: selectedNodes.includes(nodeId)
      });
    }

    // 转换连接
    for (const [connectionId, connection] of scriptGraph.connections) {
      flowEdges.push({
        id: connectionId,
        source: connection.fromNodeId,
        target: connection.toNodeId,
        sourceHandle: connection.fromPort,
        targetHandle: connection.toPort,
        type: 'smoothstep',
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20,
          color: '#666'
        },
        style: {
          strokeWidth: 2,
          stroke: '#666'
        }
      });
    }

    setNodes(flowNodes);
    setEdges(flowEdges);
  }, [selectedNodes, onNodeSelect, readonly]);

  // 从React Flow格式转换为ScriptGraph
  const convertFlowToGraph = useCallback((): ScriptGraph => {
    const scriptGraph: ScriptGraph = {
      id: graph?.id || 'new-graph',
      name: graph?.name || '新脚本',
      description: graph?.description || '',
      nodes: new Map(),
      connections: new Map(),
      variables: graph?.variables || new Map(),
      metadata: graph?.metadata || {}
    };

    // 转换节点
    for (const node of nodes) {
      if (node.data?.scriptNode) {
        const scriptNode = node.data.scriptNode as IVisualScriptNode;
        scriptNode.position = node.position;
        scriptGraph.nodes.set(node.id, scriptNode);
      }
    }

    // 转换连接
    for (const edge of edges) {
      const connection: NodeConnection = {
        id: edge.id,
        fromNodeId: edge.source,
        fromPort: edge.sourceHandle || '',
        toNodeId: edge.target,
        toPort: edge.targetHandle || '',
        type: edge.data?.type || 'trigger'
      };
      scriptGraph.connections.set(edge.id, connection);
    }

    return scriptGraph;
  }, [nodes, edges, graph]);

  // 处理节点变化
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    onNodesChange(changes);
    
    // 更新选中状态
    const newSelectedNodes: string[] = [];
    for (const change of changes) {
      if (change.type === 'select' && change.selected) {
        newSelectedNodes.push(change.id);
      }
    }
    setSelectedNodes(newSelectedNodes);

    // 通知图形变化
    setTimeout(() => {
      const updatedGraph = convertFlowToGraph();
      onGraphChange?.(updatedGraph);
    }, 0);
  }, [onNodesChange, convertFlowToGraph, onGraphChange]);

  // 处理连接变化
  const handleEdgesChange = useCallback((changes: EdgeChange[]) => {
    onEdgesChange(changes);
    
    // 通知图形变化
    setTimeout(() => {
      const updatedGraph = convertFlowToGraph();
      onGraphChange?.(updatedGraph);
    }, 0);
  }, [onEdgesChange, convertFlowToGraph, onGraphChange]);

  // 处理新连接
  const handleConnect = useCallback((connection: Connection) => {
    if (readonly) return;

    const newEdge = {
      ...connection,
      id: `edge-${Date.now()}`,
      type: 'smoothstep',
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: '#666'
      }
    };

    setEdges((eds) => addEdge(newEdge, eds));
    message.success('连接已创建');
  }, [readonly, setEdges]);

  // 处理拖拽放置
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();

    const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
    const nodeType = event.dataTransfer.getData('nodeType');

    if (!nodeType || !reactFlowInstance || !reactFlowBounds) {
      return;
    }

    const position = reactFlowInstance.project({
      x: event.clientX - reactFlowBounds.left,
      y: event.clientY - reactFlowBounds.top
    });

    // 创建新节点
    const scriptNode = nodeRegistry.createNode(nodeType);
    if (!scriptNode) {
      message.error(`无法创建节点: ${nodeType}`);
      return;
    }

    scriptNode.position = position;

    const newNode: Node = {
      id: scriptNode.id,
      type: 'visualScriptNode',
      position,
      data: {
        scriptNode,
        onSelect: () => onNodeSelect?.(scriptNode),
        onDelete: () => handleDeleteNode(scriptNode.id),
        readonly
      }
    };

    setNodes((nds) => nds.concat(newNode));
    message.success(`已添加节点: ${scriptNode.name}`);
  }, [reactFlowInstance, onNodeSelect, readonly, setNodes]);

  // 删除节点
  const handleDeleteNode = useCallback((nodeId: string) => {
    if (readonly) return;

    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个节点吗？',
      onOk: () => {
        setNodes((nds) => nds.filter((node) => node.id !== nodeId));
        setEdges((eds) => eds.filter((edge) => 
          edge.source !== nodeId && edge.target !== nodeId
        ));
        message.success('节点已删除');
      }
    });
  }, [readonly, setNodes, setEdges]);

  // 执行脚本
  const handleExecute = useCallback(async () => {
    const scriptGraph = convertFlowToGraph();
    
    if (scriptGraph.nodes.size === 0) {
      message.warning('画布为空，无法执行');
      return;
    }

    try {
      setIsExecuting(true);
      
      if (!engineRef.current) {
        engineRef.current = new VisualScriptEngine();
      }

      engineRef.current.loadGraph(scriptGraph);
      const result = await engineRef.current.execute();

      if (result.success) {
        message.success(`执行完成！耗时: ${result.executionTime.toFixed(2)}ms`);
      } else {
        message.error(`执行失败: ${result.error}`);
      }
    } catch (error) {
      message.error(`执行错误: ${error.message}`);
    } finally {
      setIsExecuting(false);
    }
  }, [convertFlowToGraph]);

  // 暂停/恢复执行
  const handlePauseResume = useCallback(() => {
    if (!engineRef.current) return;

    if (isPaused) {
      engineRef.current.resume();
      setIsPaused(false);
      message.info('执行已恢复');
    } else {
      engineRef.current.pause();
      setIsPaused(true);
      message.info('执行已暂停');
    }
  }, [isPaused]);

  // 停止执行
  const handleStop = useCallback(() => {
    if (!engineRef.current) return;

    engineRef.current.stop();
    setIsExecuting(false);
    setIsPaused(false);
    message.info('执行已停止');
  }, []);

  // 清空画布
  const handleClear = useCallback(() => {
    if (readonly) return;

    Modal.confirm({
      title: '确认清空',
      content: '确定要清空整个画布吗？此操作不可撤销。',
      onOk: () => {
        setNodes([]);
        setEdges([]);
        message.success('画布已清空');
      }
    });
  }, [readonly, setNodes, setEdges]);

  // 初始化图形数据
  React.useEffect(() => {
    if (graph) {
      convertGraphToFlow(graph);
    }
  }, [graph, convertGraphToFlow]);

  return (
    <div className="visual-canvas" style={{ width, height }}>
      {/* 工具栏 */}
      <div className="canvas-toolbar">
        <div className="toolbar-group">
          <Tooltip title="执行脚本">
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleExecute}
              loading={isExecuting}
              disabled={readonly}
            >
              执行
            </Button>
          </Tooltip>
          
          {isExecuting && (
            <Tooltip title={isPaused ? "恢复执行" : "暂停执行"}>
              <Button
                icon={<PauseCircleOutlined />}
                onClick={handlePauseResume}
              >
                {isPaused ? '恢复' : '暂停'}
              </Button>
            </Tooltip>
          )}
          
          {isExecuting && (
            <Tooltip title="停止执行">
              <Button
                icon={<StopOutlined />}
                onClick={handleStop}
                danger
              >
                停止
              </Button>
            </Tooltip>
          )}
        </div>

        <div className="toolbar-group">
          <Tooltip title="保存脚本">
            <Button icon={<SaveOutlined />} disabled={readonly}>
              保存
            </Button>
          </Tooltip>
          
          <Tooltip title="加载脚本">
            <Button icon={<FolderOpenOutlined />} disabled={readonly}>
              加载
            </Button>
          </Tooltip>
          
          <Tooltip title="清空画布">
            <Button 
              icon={<DeleteOutlined />} 
              onClick={handleClear}
              disabled={readonly}
              danger
            >
              清空
            </Button>
          </Tooltip>
        </div>
      </div>

      {/* React Flow 画布 */}
      <div 
        className="canvas-container"
        ref={reactFlowWrapper}
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
      >
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={handleNodesChange}
            onEdgesChange={handleEdgesChange}
            onConnect={handleConnect}
            onInit={setReactFlowInstance}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap 
              nodeColor="#666"
              nodeStrokeWidth={3}
              zoomable
              pannable
            />
            <Background color="#aaa" gap={16} />
          </ReactFlow>
        </ReactFlowProvider>
      </div>
    </div>
  );
};

export default VisualCanvas;
