/**
 * 视觉画布样式
 */

.visual-canvas {
  display: flex;
  flex-direction: column;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* 工具栏样式 */
.canvas-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toolbar-group .ant-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 画布容器样式 */
.canvas-container {
  flex: 1;
  position: relative;
  background: #f8f9fa;
}

/* React Flow 自定义样式 */
.react-flow__node {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #1890ff;
}

.react-flow__edge {
  stroke-width: 2;
}

.react-flow__edge.selected {
  stroke: #1890ff;
  stroke-width: 3;
}

.react-flow__edge-path {
  stroke: #666;
  stroke-width: 2;
}

.react-flow__connection-line {
  stroke: #1890ff;
  stroke-width: 2;
  stroke-dasharray: 5,5;
}

/* 控制面板样式 */
.react-flow__controls {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__controls-button {
  background: white;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  color: #666;
  transition: all 0.2s ease;
}

.react-flow__controls-button:hover {
  background: #f5f5f5;
  color: #1890ff;
}

.react-flow__controls-button:last-child {
  border-bottom: none;
}

/* 小地图样式 */
.react-flow__minimap {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__minimap-mask {
  fill: rgba(24, 144, 255, 0.1);
  stroke: #1890ff;
  stroke-width: 2;
}

/* 背景样式 */
.react-flow__background {
  background-color: #f8f9fa;
}

/* 拖拽提示 */
.canvas-container::before {
  content: '从左侧面板拖拽节点到此处';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #bfbfbf;
  font-size: 16px;
  pointer-events: none;
  z-index: 0;
}

.canvas-container:not(:empty)::before {
  display: none;
}

/* 执行状态指示器 */
.execution-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1000;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #d9d9d9;
}

.execution-indicator.executing {
  border-color: #52c41a;
  background: #f6ffed;
}

.execution-indicator.paused {
  border-color: #faad14;
  background: #fffbe6;
}

.execution-indicator.error {
  border-color: #ff4d4f;
  background: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px;
  }
  
  .toolbar-group {
    width: 100%;
    justify-content: center;
  }
  
  .canvas-container::before {
    font-size: 14px;
    text-align: center;
    padding: 0 20px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .visual-canvas {
    background: #141414;
    border-color: #303030;
  }
  
  .canvas-toolbar {
    background: #1f1f1f;
    border-bottom-color: #303030;
  }
  
  .canvas-container {
    background: #0f1419;
  }
  
  .canvas-container::before {
    color: #595959;
  }
  
  .react-flow__controls {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .react-flow__controls-button {
    background: #1f1f1f;
    border-bottom-color: #303030;
    color: #a6a6a6;
  }
  
  .react-flow__controls-button:hover {
    background: #262626;
    color: #40a9ff;
  }
  
  .react-flow__minimap {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .react-flow__background {
    background-color: #0f1419;
  }
  
  .execution-indicator {
    background: #1f1f1f;
    border-color: #303030;
    color: #f0f0f0;
  }
}

/* 动画效果 */
@keyframes nodeHighlight {
  0% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.5);
  }
  100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.react-flow__node.executing {
  animation: nodeHighlight 1s ease-in-out infinite;
}

/* 连接线动画 */
@keyframes connectionFlow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 20;
  }
}

.react-flow__edge.active {
  stroke-dasharray: 5,5;
  animation: connectionFlow 1s linear infinite;
}

/* 工具提示样式 */
.canvas-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
}

/* 选择框样式 */
.react-flow__selection {
  background: rgba(24, 144, 255, 0.1);
  border: 1px dashed #1890ff;
}

/* 节点拖拽样式 */
.react-flow__node.dragging {
  opacity: 0.8;
  transform: rotate(5deg);
}

/* 连接点样式 */
.react-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.react-flow__handle:hover {
  width: 16px;
  height: 16px;
  border-width: 3px;
}

.react-flow__handle.connecting {
  background: #1890ff;
  border-color: #1890ff;
}

/* 错误状态样式 */
.react-flow__node.error {
  border: 2px solid #ff4d4f;
  background: #fff2f0;
}

.react-flow__edge.error {
  stroke: #ff4d4f;
  stroke-width: 3;
  stroke-dasharray: 3,3;
}
