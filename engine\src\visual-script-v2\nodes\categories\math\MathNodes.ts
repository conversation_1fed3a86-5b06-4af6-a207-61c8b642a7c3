/**
 * 数学运算节点
 * 提供基础的数学计算功能
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 加法节点
 */
export class AddNode extends BaseNode {
  public readonly type = 'math/add';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '加法';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'a',
      label: '数值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个加数'
    });

    this.addInputPort({
      name: 'b',
      label: '数值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个加数'
    });

    // 输出端口
    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '加法运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = a + b;
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 减法节点
 */
export class SubtractNode extends BaseNode {
  public readonly type = 'math/subtract';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '减法';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '被减数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '被减数'
    });

    this.addInputPort({
      name: 'b',
      label: '减数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '减数'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '减法运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = a - b;
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 乘法节点
 */
export class MultiplyNode extends BaseNode {
  public readonly type = 'math/multiply';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '乘法';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '乘数A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '第一个乘数'
    });

    this.addInputPort({
      name: 'b',
      label: '乘数B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '第二个乘数'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '乘法运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 1);
    const b = this.getInput(context, 'b', 1);
    const result = a * b;
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 除法节点
 */
export class DivideNode extends BaseNode {
  public readonly type = 'math/divide';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '除法';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '被除数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '被除数'
    });

    this.addInputPort({
      name: 'b',
      label: '除数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '除数'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '除法运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 1);
    const b = this.getInput(context, 'b', 1);
    
    if (b === 0) {
      this.log(context, 'error', '除数不能为零');
      this.setOutput(context, 'result', NaN);
    } else {
      const result = a / b;
      this.setOutput(context, 'result', result);
    }
  }
}

/**
 * 取模节点
 */
export class ModuloNode extends BaseNode {
  public readonly type = 'math/modulo';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '取模';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '被除数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 10,
      description: '被除数'
    });

    this.addInputPort({
      name: 'b',
      label: '除数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 3,
      description: '除数'
    });

    this.addOutputPort({
      name: 'result',
      label: '余数',
      type: DataType.NUMBER,
      required: false,
      description: '取模运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 10);
    const b = this.getInput(context, 'b', 3);
    
    if (b === 0) {
      this.log(context, 'error', '除数不能为零');
      this.setOutput(context, 'result', NaN);
    } else {
      const result = a % b;
      this.setOutput(context, 'result', result);
    }
  }
}

/**
 * 幂运算节点
 */
export class PowerNode extends BaseNode {
  public readonly type = 'math/power';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '幂运算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'base',
      label: '底数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 2,
      description: '底数'
    });

    this.addInputPort({
      name: 'exponent',
      label: '指数',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 2,
      description: '指数'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '幂运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const base = this.getInput(context, 'base', 2);
    const exponent = this.getInput(context, 'exponent', 2);
    const result = Math.pow(base, exponent);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 平方根节点
 */
export class SquareRootNode extends BaseNode {
  public readonly type = 'math/sqrt';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '平方根';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'value',
      label: '数值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 4,
      description: '要计算平方根的数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '平方根结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInput(context, 'value', 4);
    
    if (value < 0) {
      this.log(context, 'error', '不能计算负数的平方根');
      this.setOutput(context, 'result', NaN);
    } else {
      const result = Math.sqrt(value);
      this.setOutput(context, 'result', result);
    }
  }
}

/**
 * 绝对值节点
 */
export class AbsNode extends BaseNode {
  public readonly type = 'math/abs';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '绝对值';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'value',
      label: '数值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: -5,
      description: '要计算绝对值的数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '绝对值结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInput(context, 'value', -5);
    const result = Math.abs(value);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 最小值节点
 */
export class MinNode extends BaseNode {
  public readonly type = 'math/min';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '最小值';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '数值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个数值'
    });

    this.addInputPort({
      name: 'b',
      label: '数值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '最小值',
      type: DataType.NUMBER,
      required: false,
      description: '两个数值中的最小值'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = Math.min(a, b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 最大值节点
 */
export class MaxNode extends BaseNode {
  public readonly type = 'math/max';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '最大值';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '数值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个数值'
    });

    this.addInputPort({
      name: 'b',
      label: '数值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '最大值',
      type: DataType.NUMBER,
      required: false,
      description: '两个数值中的最大值'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = Math.max(a, b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 限制范围节点
 */
export class ClampNode extends BaseNode {
  public readonly type = 'math/clamp';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '限制范围';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'value',
      label: '数值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 5,
      description: '要限制的数值'
    });

    this.addInputPort({
      name: 'min',
      label: '最小值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '允许的最小值'
    });

    this.addInputPort({
      name: 'max',
      label: '最大值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 10,
      description: '允许的最大值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '限制后的数值'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInput(context, 'value', 5);
    const min = this.getInput(context, 'min', 0);
    const max = this.getInput(context, 'max', 10);
    
    const result = Math.max(min, Math.min(max, value));
    this.setOutput(context, 'result', result);
  }
}

/**
 * 线性插值节点
 */
export class LerpNode extends BaseNode {
  public readonly type = 'math/lerp';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '线性插值';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '起始值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '插值起始值'
    });

    this.addInputPort({
      name: 'b',
      label: '结束值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 10,
      description: '插值结束值'
    });

    this.addInputPort({
      name: 't',
      label: '插值因子',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0.5,
      description: '插值因子（0-1）'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '插值结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 10);
    const t = this.getInput(context, 't', 0.5);
    
    const result = a + (b - a) * t;
    this.setOutput(context, 'result', result);
  }
}

/**
 * 正弦函数节点
 */
export class SinNode extends BaseNode {
  public readonly type = 'math/sin';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '正弦';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'angle',
      label: '角度',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '角度（弧度）'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '正弦值'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const angle = this.getInput(context, 'angle', 0);
    const result = Math.sin(angle);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 余弦函数节点
 */
export class CosNode extends BaseNode {
  public readonly type = 'math/cos';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '余弦';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'angle',
      label: '角度',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '角度（弧度）'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '余弦值'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const angle = this.getInput(context, 'angle', 0);
    const result = Math.cos(angle);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 随机数节点
 */
export class RandomNode extends BaseNode {
  public readonly type = 'math/random';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '随机数';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'min',
      label: '最小值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '随机数最小值'
    });

    this.addInputPort({
      name: 'max',
      label: '最大值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '随机数最大值'
    });

    this.addOutputPort({
      name: 'result',
      label: '随机数',
      type: DataType.NUMBER,
      required: false,
      description: '生成的随机数'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'integer',
      label: '整数',
      type: 'boolean',
      value: false,
      defaultValue: false,
      description: '是否生成整数'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const min = this.getInput(context, 'min', 0);
    const max = this.getInput(context, 'max', 1);
    const isInteger = this.getProperty('integer');
    
    let result = Math.random() * (max - min) + min;
    
    if (isInteger) {
      result = Math.floor(result);
    }
    
    this.setOutput(context, 'result', result);
  }
}
