/**
 * 属性编辑器样式
 */

.property-editor {
  height: 100%;
  background: #fafafa;
  border-left: 1px solid #d9d9d9;
}

.property-card {
  height: 100%;
  border: none;
  border-radius: 0;
}

.property-card .ant-card-body {
  padding: 12px;
  height: calc(100% - 46px);
  overflow-y: auto;
}

/* 编辑器头部 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.editor-header span {
  font-weight: 500;
  color: #262626;
}

/* 节点信息区域 */
.node-info {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #8c8c8c;
  font-weight: 500;
}

.info-value {
  color: #262626;
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 11px;
}

/* 属性表单样式 */
.properties-form {
  margin-bottom: 16px;
}

.properties-form .ant-form-item {
  margin-bottom: 12px;
}

.properties-form .ant-form-item-label {
  padding-bottom: 4px;
}

.properties-form .ant-form-item-label > label {
  font-size: 12px;
  font-weight: 500;
  color: #595959;
}

/* 属性字段样式 */
.property-field {
  transition: all 0.2s ease;
}

.property-field:hover {
  background: rgba(24, 144, 255, 0.02);
  border-radius: 4px;
  margin: 0 -4px;
  padding: 0 4px;
}

.property-field.readonly {
  opacity: 0.6;
}

.property-field.readonly:hover {
  background: transparent;
}

/* 输入控件样式 */
.property-field .ant-input,
.property-field .ant-input-number,
.property-field .ant-select-selector {
  font-size: 12px;
  border-radius: 4px;
}

.property-field .ant-input-number {
  width: 100%;
}

.property-field .ant-switch {
  margin-top: 4px;
}

/* 颜色选择器样式 */
.property-field .ant-color-picker {
  width: 100%;
}

.property-field .ant-color-picker-trigger {
  width: 100%;
  height: 32px;
  border-radius: 4px;
}

/* 向量输入样式 */
.property-field .ant-space-compact {
  width: 100%;
}

.property-field .ant-input-number-group-addon {
  font-size: 10px;
  font-weight: 500;
  color: #8c8c8c;
  background: #f5f5f5;
  border-color: #d9d9d9;
  min-width: 20px;
  padding: 0 4px;
}

/* 端口信息区域 */
.ports-info {
  margin-top: 8px;
}

.ports-section {
  margin-bottom: 12px;
}

.ports-section h4 {
  font-size: 12px;
  font-weight: 500;
  color: #595959;
  margin-bottom: 6px;
  padding-bottom: 2px;
  border-bottom: 1px solid #f0f0f0;
}

.ports-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.port-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  background: #f8f9fa;
  border-radius: 3px;
  font-size: 11px;
}

.port-name {
  color: #262626;
  font-weight: 500;
}

.port-type {
  color: #8c8c8c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #e6f7ff;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
}

/* 分割线样式 */
.property-editor .ant-divider {
  margin: 12px 0;
  border-color: #f0f0f0;
}

/* 空状态样式 */
.property-editor .ant-empty {
  margin: 20px 0;
}

.property-editor .ant-empty-description {
  color: #8c8c8c;
  font-size: 12px;
}

/* 滚动条样式 */
.property-card .ant-card-body::-webkit-scrollbar {
  width: 6px;
}

.property-card .ant-card-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.property-card .ant-card-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.property-card .ant-card-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具提示样式 */
.property-field .anticon-info-circle {
  cursor: help;
  transition: color 0.2s ease;
}

.property-field .anticon-info-circle:hover {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .property-editor {
    width: 100% !important;
    border-left: none;
    border-top: 1px solid #d9d9d9;
  }
  
  .property-card .ant-card-body {
    padding: 8px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
  
  .info-value {
    align-self: flex-end;
  }
  
  .port-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .property-editor {
    background: #141414;
    border-left-color: #303030;
  }
  
  .property-card {
    background: #1f1f1f;
  }
  
  .property-card .ant-card-head {
    background: #1f1f1f;
    border-bottom-color: #303030;
  }
  
  .editor-header span {
    color: #f0f0f0;
  }
  
  .node-info {
    background: #262626;
  }
  
  .info-label {
    color: #a6a6a6;
  }
  
  .info-value {
    color: #f0f0f0;
    background: #3a3a3a;
  }
  
  .properties-form .ant-form-item-label > label {
    color: #a6a6a6;
  }
  
  .property-field:hover {
    background: rgba(64, 169, 255, 0.05);
  }
  
  .ports-section h4 {
    color: #a6a6a6;
    border-bottom-color: #303030;
  }
  
  .port-item {
    background: #262626;
  }
  
  .port-name {
    color: #f0f0f0;
  }
  
  .port-type {
    color: #a6a6a6;
    background: #1f3a3a;
  }
  
  .property-editor .ant-divider {
    border-color: #303030;
  }
  
  .property-editor .ant-empty-description {
    color: #a6a6a6;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .property-field {
    border: 1px solid transparent;
  }
  
  .property-field:hover {
    border-color: #1890ff;
  }
  
  .port-item {
    border: 1px solid #d9d9d9;
  }
  
  .info-value,
  .port-type {
    border: 1px solid #d9d9d9;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .property-field,
  .property-field .anticon-info-circle {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .property-editor {
    border: none;
    background: white;
  }
  
  .editor-header button {
    display: none;
  }
  
  .property-card .ant-card-body {
    overflow: visible;
    height: auto;
  }
  
  .ports-info {
    page-break-inside: avoid;
  }
}
