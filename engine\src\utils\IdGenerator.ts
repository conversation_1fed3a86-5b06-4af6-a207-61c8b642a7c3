/**
 * ID生成器工具
 * 提供各种格式的唯一ID生成功能
 */

/**
 * 生成UUID v4格式的唯一ID
 */
export function generateId(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 生成短格式的唯一ID
 */
export function generateShortId(): string {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * 生成带前缀的ID
 */
export function generatePrefixedId(prefix: string): string {
  return `${prefix}_${generateShortId()}`;
}

/**
 * 生成时间戳ID
 */
export function generateTimestampId(): string {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
}

/**
 * 生成节点ID
 */
export function generateNodeId(): string {
  return generatePrefixedId('node');
}

/**
 * 生成连接ID
 */
export function generateConnectionId(): string {
  return generatePrefixedId('conn');
}

/**
 * 生成图形ID
 */
export function generateGraphId(): string {
  return generatePrefixedId('graph');
}
