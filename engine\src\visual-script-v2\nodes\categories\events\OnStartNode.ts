/**
 * 开始事件节点
 * 在脚本开始执行时触发
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

export class OnStartNode extends BaseNode {
  public readonly type = 'events/onStart';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '开始事件';
  }

  protected initializePorts(): void {
    // 输出端口
    this.addOutputPort({
      name: 'onStart',
      label: '开始',
      type: DataType.TRIGGER,
      required: false,
      description: '脚本开始时触发'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    // 开始事件节点在脚本启动时自动执行
    this.log(context, 'info', '脚本开始执行');
    this.triggerOutput(context, 'onStart');
  }
}

/**
 * 更新事件节点
 * 在每帧更新时触发
 */
export class OnUpdateNode extends BaseNode {
  public readonly type = 'events/onUpdate';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '更新事件';
  }

  protected initializePorts(): void {
    // 输出端口
    this.addOutputPort({
      name: 'onUpdate',
      label: '更新',
      type: DataType.TRIGGER,
      required: false,
      description: '每帧更新时触发'
    });

    this.addOutputPort({
      name: 'deltaTime',
      label: '帧时间',
      type: DataType.NUMBER,
      required: false,
      description: '当前帧的时间间隔'
    });

    this.addOutputPort({
      name: 'totalTime',
      label: '总时间',
      type: DataType.NUMBER,
      required: false,
      description: '脚本运行的总时间'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const timeInfo = context.getTime();
    
    this.setOutput(context, 'deltaTime', timeInfo.deltaTime);
    this.setOutput(context, 'totalTime', timeInfo.totalTime);
    this.triggerOutput(context, 'onUpdate');
  }
}

/**
 * 销毁事件节点
 * 在脚本销毁时触发
 */
export class OnDestroyNode extends BaseNode {
  public readonly type = 'events/onDestroy';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '销毁事件';
  }

  protected initializePorts(): void {
    // 输出端口
    this.addOutputPort({
      name: 'onDestroy',
      label: '销毁',
      type: DataType.TRIGGER,
      required: false,
      description: '脚本销毁时触发'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    this.log(context, 'info', '脚本即将销毁');
    this.triggerOutput(context, 'onDestroy');
  }
}

/**
 * 点击事件节点
 * 在鼠标点击时触发
 */
export class OnClickNode extends BaseNode {
  public readonly type = 'events/onClick';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '点击事件';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'target',
      label: '目标对象',
      type: DataType.ENTITY,
      required: false,
      description: '监听点击的目标对象，为空则监听全局点击'
    });

    // 输出端口
    this.addOutputPort({
      name: 'onClick',
      label: '点击',
      type: DataType.TRIGGER,
      required: false,
      description: '点击时触发'
    });

    this.addOutputPort({
      name: 'position',
      label: '点击位置',
      type: DataType.VECTOR2,
      required: false,
      description: '点击的屏幕坐标'
    });

    this.addOutputPort({
      name: 'button',
      label: '鼠标按键',
      type: DataType.NUMBER,
      required: false,
      description: '点击的鼠标按键（0=左键，1=中键，2=右键）'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'button',
      label: '监听按键',
      type: 'select',
      value: 0,
      defaultValue: 0,
      description: '监听的鼠标按键',
      options: [
        { label: '左键', value: 0 },
        { label: '中键', value: 1 },
        { label: '右键', value: 2 },
        { label: '任意键', value: -1 }
      ]
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    // 这里应该注册点击事件监听器
    // 在实际实现中，这个节点会在初始化时注册事件监听器
    // 当点击事件发生时，会触发执行
    
    const targetButton = this.getProperty('button');
    
    // 模拟点击事件数据（实际应该从事件系统获取）
    const clickData = {
      position: { x: 0, y: 0 }, // 实际的点击位置
      button: 0 // 实际的按键
    };

    // 检查按键匹配
    if (targetButton === -1 || clickData.button === targetButton) {
      this.setOutput(context, 'position', clickData.position);
      this.setOutput(context, 'button', clickData.button);
      this.triggerOutput(context, 'onClick');
    }
  }
}

/**
 * 悬停事件节点
 * 在鼠标悬停时触发
 */
export class OnHoverNode extends BaseNode {
  public readonly type = 'events/onHover';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '悬停事件';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'target',
      label: '目标对象',
      type: DataType.ENTITY,
      required: false,
      description: '监听悬停的目标对象'
    });

    // 输出端口
    this.addOutputPort({
      name: 'onHoverEnter',
      label: '悬停进入',
      type: DataType.TRIGGER,
      required: false,
      description: '鼠标进入时触发'
    });

    this.addOutputPort({
      name: 'onHoverExit',
      label: '悬停离开',
      type: DataType.TRIGGER,
      required: false,
      description: '鼠标离开时触发'
    });

    this.addOutputPort({
      name: 'position',
      label: '鼠标位置',
      type: DataType.VECTOR2,
      required: false,
      description: '当前鼠标位置'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    // 悬停事件的实际实现需要与输入系统集成
    // 这里提供基本的执行框架
    
    const mousePosition = { x: 0, y: 0 }; // 从输入系统获取
    this.setOutput(context, 'position', mousePosition);
    
    // 根据悬停状态触发相应的输出
    // 实际实现需要跟踪悬停状态
  }
}

/**
 * 按键事件节点
 * 在键盘按键时触发
 */
export class OnKeyNode extends BaseNode {
  public readonly type = 'events/onKey';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '按键事件';
  }

  protected initializePorts(): void {
    // 输出端口
    this.addOutputPort({
      name: 'onKeyDown',
      label: '按键按下',
      type: DataType.TRIGGER,
      required: false,
      description: '按键按下时触发'
    });

    this.addOutputPort({
      name: 'onKeyUp',
      label: '按键释放',
      type: DataType.TRIGGER,
      required: false,
      description: '按键释放时触发'
    });

    this.addOutputPort({
      name: 'key',
      label: '按键代码',
      type: DataType.STRING,
      required: false,
      description: '按下的按键代码'
    });

    this.addOutputPort({
      name: 'keyCode',
      label: '按键码',
      type: DataType.NUMBER,
      required: false,
      description: '按下的按键数字码'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'targetKey',
      label: '目标按键',
      type: 'string',
      value: '',
      defaultValue: '',
      description: '要监听的特定按键，为空则监听所有按键'
    });

    this.addProperty({
      name: 'eventType',
      label: '事件类型',
      type: 'select',
      value: 'both',
      defaultValue: 'both',
      description: '监听的事件类型',
      options: [
        { label: '按下和释放', value: 'both' },
        { label: '仅按下', value: 'down' },
        { label: '仅释放', value: 'up' }
      ]
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const targetKey = this.getProperty('targetKey');
    const eventType = this.getProperty('eventType');
    
    // 模拟按键事件数据
    const keyData = {
      key: 'Space',
      keyCode: 32,
      type: 'down' // 'down' 或 'up'
    };

    // 检查按键匹配
    if (!targetKey || keyData.key === targetKey) {
      this.setOutput(context, 'key', keyData.key);
      this.setOutput(context, 'keyCode', keyData.keyCode);

      // 根据事件类型触发相应输出
      if ((eventType === 'both' || eventType === 'down') && keyData.type === 'down') {
        this.triggerOutput(context, 'onKeyDown');
      }
      if ((eventType === 'both' || eventType === 'up') && keyData.type === 'up') {
        this.triggerOutput(context, 'onKeyUp');
      }
    }
  }
}

/**
 * 自定义事件节点
 * 监听自定义事件
 */
export class CustomEventNode extends BaseNode {
  public readonly type = 'events/customEvent';
  public readonly category = NodeCategory.EVENTS;

  protected getDefaultName(): string {
    return '自定义事件';
  }

  protected initializePorts(): void {
    // 输出端口
    this.addOutputPort({
      name: 'onEvent',
      label: '事件触发',
      type: DataType.TRIGGER,
      required: false,
      description: '自定义事件触发时执行'
    });

    this.addOutputPort({
      name: 'eventData',
      label: '事件数据',
      type: DataType.OBJECT,
      required: false,
      description: '事件携带的数据'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'eventName',
      label: '事件名称',
      type: 'string',
      value: '',
      defaultValue: '',
      description: '要监听的自定义事件名称'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const eventName = this.getProperty('eventName');
    
    if (eventName) {
      // 注册事件监听器
      context.addEventListener(eventName, (data) => {
        this.setOutput(context, 'eventData', data);
        this.triggerOutput(context, 'onEvent');
      });
    }
  }
}
