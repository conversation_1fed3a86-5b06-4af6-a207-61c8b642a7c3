# DL引擎视觉脚本系统重构方案

**文档版本**: 1.0  
**创建日期**: 2025年7月9日  
**重构目标**: 基于visualscript核心，整合visual-script节点，构建统一的视觉脚本系统

## 📋 重构概述

### 现状问题
- **三套系统并存**: visual-script、visualization、visualscript功能重叠
- **架构混乱**: 多重节点注册系统，接口不统一
- **维护困难**: 代码重复，依赖关系复杂
- **开发体验差**: 学习成本高，集成复杂

### 重构目标
- **统一架构**: 基于visualscript核心构建统一系统
- **节点整合**: 整合250+个节点到统一框架
- **编辑器集成**: 提供流畅的拖拽式编程体验
- **性能优化**: 统一优化，提升执行效率

## 🏗️ 新架构设计

### 核心架构
```
engine/src/visual-script-v2/
├── core/                     # 核心系统
│   ├── Engine.ts            # 执行引擎
│   ├── Graph.ts             # 图形系统
│   ├── Context.ts           # 执行上下文
│   └── System.ts            # ECS系统集成
├── nodes/                    # 节点系统
│   ├── base/                # 基础节点类
│   ├── registry/            # 注册系统
│   └── categories/          # 分类节点
├── editor/                   # 编辑器集成
│   ├── Panel.ts             # 节点面板
│   ├── Canvas.ts            # 视觉画布
│   └── Properties.ts        # 属性编辑器
├── visualization/            # 数据可视化
│   ├── Charts.ts            # 图表组件
│   └── Realtime.ts          # 实时数据
└── tools/                    # 开发工具
    ├── Debugger.ts          # 调试器
    └── Profiler.ts          # 性能分析
```

### 技术栈
- **核心**: TypeScript + 现有visualscript引擎
- **编辑器**: React + Ant Design + React Flow
- **可视化**: D3.js + Canvas API
- **测试**: Jest + Testing Library

## 📅 分阶段实施计划

### 阶段1: 系统清理与准备 (1周)

#### 1.1 代码清理
- **删除重复实现**: 清理visual-script中的重复代码
- **依赖梳理**: 解决循环依赖问题
- **接口统一**: 定义统一的节点和系统接口

#### 1.2 基础架构
- **创建新目录**: 建立visual-script-v2目录结构
- **核心接口**: 定义统一的节点基类和执行接口
- **类型系统**: 建立完整的TypeScript类型定义

### 阶段2: 核心架构重构 (2周)

#### 2.1 执行引擎重构
```typescript
// 统一的执行引擎
export class VisualScriptEngine {
  private graph: ScriptGraph;
  private context: ExecutionContext;
  private scheduler: ExecutionScheduler;
  
  async execute(): Promise<void> {
    // 统一的执行逻辑
  }
}
```

#### 2.2 节点系统重构
```typescript
// 统一的节点基类
export abstract class BaseNode {
  abstract readonly type: string;
  abstract readonly category: NodeCategory;
  
  abstract execute(context: ExecutionContext): Promise<void>;
}

// 简化的注册系统
export class NodeRegistry {
  private static nodes = new Map<string, NodeDefinition>();
  
  static register(definition: NodeDefinition): void {
    // 简化的注册逻辑
  }
}
```

### 阶段3: 节点迁移与实现 (10周，分5批次)

#### 批次1: 核心基础节点 (50个) - 2周
**目标**: 实现最基础的核心功能节点

##### 3.1.1 事件节点 (10个)
- OnStart - 开始事件
- OnUpdate - 更新事件
- OnDestroy - 销毁事件
- OnClick - 点击事件
- OnHover - 悬停事件
- OnKeyDown - 按键按下
- OnKeyUp - 按键释放
- OnCollision - 碰撞事件
- OnTrigger - 触发器事件
- CustomEvent - 自定义事件

##### 3.1.2 流程控制节点 (15个)
- Sequence - 顺序执行
- Branch - 条件分支
- ForLoop - For循环
- WhileLoop - While循环
- DoWhile - DoWhile循环
- Switch - 开关选择
- Delay - 延迟执行
- Gate - 门控制
- FlipFlop - 触发器
- MultiGate - 多路门
- DoOnce - 执行一次
- DoN - 执行N次
- Retriggerable - 可重触发延迟
- Timeline - 时间轴
- Parallel - 并行执行

##### 3.1.3 数学运算节点 (15个)
- Add - 加法
- Subtract - 减法
- Multiply - 乘法
- Divide - 除法
- Modulo - 取模
- Power - 幂运算
- SquareRoot - 平方根
- Abs - 绝对值
- Min - 最小值
- Max - 最大值
- Clamp - 限制范围
- Lerp - 线性插值
- Sin - 正弦
- Cos - 余弦
- Random - 随机数

##### 3.1.4 逻辑运算节点 (10个)
- And - 逻辑与
- Or - 逻辑或
- Not - 逻辑非
- Equal - 等于
- NotEqual - 不等于
- Greater - 大于
- Less - 小于
- GreaterEqual - 大于等于
- LessEqual - 小于等于
- IsValid - 有效性检查

#### 批次2: 渲染系统节点 (50个) - 2周
**目标**: 实现完整的渲染功能节点

##### 3.2.1 材质节点 (15个)
- CreateMaterial - 创建材质
- SetMaterialProperty - 设置材质属性
- GetMaterialProperty - 获取材质属性
- StandardMaterial - 标准材质
- PBRMaterial - PBR材质
- UnlitMaterial - 无光材质
- TransparentMaterial - 透明材质
- EmissiveMaterial - 发光材质
- MaterialInstance - 材质实例
- MaterialParameter - 材质参数
- TextureSampler - 纹理采样
- NormalMap - 法线贴图
- HeightMap - 高度图
- CubeMap - 立方体贴图
- MaterialBlend - 材质混合

##### 3.2.2 光照节点 (15个)
- DirectionalLight - 方向光
- PointLight - 点光源
- SpotLight - 聚光灯
- AreaLight - 面光源
- AmbientLight - 环境光
- SetLightIntensity - 设置光强
- SetLightColor - 设置光颜色
- SetLightPosition - 设置光位置
- SetLightDirection - 设置光方向
- LightShadow - 光影设置
- VolumetricLight - 体积光
- LightProbe - 光探针
- ReflectionProbe - 反射探针
- LightmapBake - 光照贴图烘焙
- GlobalIllumination - 全局光照

##### 3.2.3 相机节点 (10个)
- CreateCamera - 创建相机
- SetCameraPosition - 设置相机位置
- SetCameraRotation - 设置相机旋转
- SetCameraFOV - 设置视野角度
- SetCameraNearFar - 设置近远裁剪面
- CameraLookAt - 相机看向
- OrthographicCamera - 正交相机
- PerspectiveCamera - 透视相机
- CameraShake - 相机震动
- CameraFollow - 相机跟随

##### 3.2.4 后处理节点 (10个)
- Bloom - 泛光效果
- ToneMapping - 色调映射
- ColorGrading - 颜色分级
- SSAO - 屏幕空间环境光遮蔽
- SSR - 屏幕空间反射
- MotionBlur - 运动模糊
- DepthOfField - 景深
- Vignette - 暗角
- ChromaticAberration - 色差
- FilmGrain - 胶片颗粒

#### 批次3: 物理与动画节点 (50个) - 2周
**目标**: 实现物理模拟和动画系统节点

##### 3.3.1 物理节点 (25个)
- CreateRigidBody - 创建刚体
- SetRigidBodyMass - 设置质量
- ApplyForce - 施加力
- ApplyImpulse - 施加冲量
- ApplyTorque - 施加扭矩
- SetVelocity - 设置速度
- GetVelocity - 获取速度
- SetAngularVelocity - 设置角速度
- BoxCollider - 盒子碰撞器
- SphereCollider - 球体碰撞器
- CapsuleCollider - 胶囊碰撞器
- MeshCollider - 网格碰撞器
- PlaneCollider - 平面碰撞器
- HingeJoint - 铰链关节
- SpringJoint - 弹簧关节
- FixedJoint - 固定关节
- SliderJoint - 滑动关节
- BallJoint - 球关节
- SetGravity - 设置重力
- SetFriction - 设置摩擦力
- SetRestitution - 设置弹性
- RaycastHit - 射线检测
- OverlapSphere - 球体重叠检测
- PhysicsStep - 物理步进
- PhysicsDebugDraw - 物理调试绘制

##### 3.3.2 动画节点 (25个)
- PlayAnimation - 播放动画
- StopAnimation - 停止动画
- PauseAnimation - 暂停动画
- SetAnimationSpeed - 设置动画速度
- SetAnimationTime - 设置动画时间
- BlendAnimations - 混合动画
- CrossfadeAnimation - 交叉淡化动画
- AnimationState - 动画状态
- AnimationTransition - 动画过渡
- BoneTransform - 骨骼变换
- IKSolver - IK求解器
- LookAtIK - 看向IK
- TwoBoneIK - 双骨IK
- FullBodyIK - 全身IK
- AnimationCurve - 动画曲线
- Keyframe - 关键帧
- TweenPosition - 位置补间
- TweenRotation - 旋转补间
- TweenScale - 缩放补间
- TweenColor - 颜色补间
- EaseInOut - 缓入缓出
- AnimationEvent - 动画事件
- MorphTarget - 变形目标
- FacialAnimation - 面部动画
- ParticleSystem - 粒子系统

#### 批次4: AI与网络节点 (50个) - 2周
**目标**: 实现AI功能和网络通信节点

##### 3.4.1 AI节点 (25个)
- AIAgent - AI代理
- Pathfinding - 路径寻找
- NavMesh - 导航网格
- Behavior Tree - 行为树
- State Machine - 状态机
- Decision Tree - 决策树
- Neural Network - 神经网络
- Machine Learning - 机器学习
- Image Recognition - 图像识别
- Object Detection - 物体检测
- Face Recognition - 人脸识别
- Speech Recognition - 语音识别
- Text Analysis - 文本分析
- Sentiment Analysis - 情感分析
- Language Detection - 语言检测
- Translation - 翻译
- ChatBot - 聊天机器人
- Recommendation - 推荐系统
- Clustering - 聚类分析
- Classification - 分类
- Regression - 回归分析
- Reinforcement Learning - 强化学习
- Genetic Algorithm - 遗传算法
- Fuzzy Logic - 模糊逻辑
- Expert System - 专家系统

##### 3.4.2 网络节点 (25个)
- HTTP Request - HTTP请求
- HTTP Response - HTTP响应
- WebSocket Connect - WebSocket连接
- WebSocket Send - WebSocket发送
- WebSocket Receive - WebSocket接收
- REST API - REST接口
- GraphQL Query - GraphQL查询
- Database Query - 数据库查询
- Database Insert - 数据库插入
- Database Update - 数据库更新
- Database Delete - 数据库删除
- File Upload - 文件上传
- File Download - 文件下载
- JSON Parse - JSON解析
- JSON Stringify - JSON序列化
- XML Parse - XML解析
- CSV Parse - CSV解析
- URL Encode - URL编码
- Base64 Encode - Base64编码
- Hash Generate - 哈希生成
- Encryption - 加密
- Decryption - 解密
- Session Management - 会话管理
- Authentication - 身份验证
- Authorization - 权限验证

#### 批次5: UI与数据处理节点 (50个) - 2周
**目标**: 实现用户界面和数据处理节点

##### 3.5.1 UI节点 (25个)
- Create Button - 创建按钮
- Create Text - 创建文本
- Create Image - 创建图像
- Create Panel - 创建面板
- Create Input - 创建输入框
- Create Slider - 创建滑块
- Create Dropdown - 创建下拉框
- Create Checkbox - 创建复选框
- Create Radio - 创建单选框
- Create Progress - 创建进度条
- Set UI Property - 设置UI属性
- Get UI Property - 获取UI属性
- UI Event Handler - UI事件处理
- Layout Manager - 布局管理器
- Flex Layout - 弹性布局
- Grid Layout - 网格布局
- Absolute Layout - 绝对布局
- UI Animation - UI动画
- Modal Dialog - 模态对话框
- Tooltip - 工具提示
- Context Menu - 上下文菜单
- Notification - 通知
- Loading Spinner - 加载动画
- Data Binding - 数据绑定
- Form Validation - 表单验证

##### 3.5.2 数据处理节点 (25个)
- Array Create - 创建数组
- Array Push - 数组添加
- Array Pop - 数组弹出
- Array Sort - 数组排序
- Array Filter - 数组过滤
- Array Map - 数组映射
- Array Reduce - 数组归约
- Object Create - 创建对象
- Object Get - 获取对象属性
- Object Set - 设置对象属性
- String Concat - 字符串连接
- String Split - 字符串分割
- String Replace - 字符串替换
- String Format - 字符串格式化
- Number Format - 数字格式化
- Date Format - 日期格式化
- Data Validation - 数据验证
- Data Transform - 数据转换
- Data Aggregate - 数据聚合
- Data Sort - 数据排序
- Data Group - 数据分组
- Data Join - 数据连接
- Cache Set - 缓存设置
- Cache Get - 缓存获取
- Local Storage - 本地存储

### 阶段4: 编辑器集成 (3周)

#### 4.1 节点面板重构
```typescript
// 统一的节点面板
export const NodePanel: React.FC = () => {
  const [categories] = useState(NodeRegistry.getCategories());
  const [searchTerm, setSearchTerm] = useState('');
  
  return (
    <div className="node-panel">
      <SearchBox onSearch={setSearchTerm} />
      <CategoryTabs categories={categories} />
      <NodeList searchTerm={searchTerm} />
    </div>
  );
};
```

#### 4.2 视觉画布重构
```typescript
// 基于React Flow的画布
export const VisualCanvas: React.FC = () => {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  
  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
    >
      <Controls />
      <MiniMap />
      <Background />
    </ReactFlow>
  );
};
```

#### 4.3 属性编辑器重构
```typescript
// 动态属性编辑器
export const PropertyEditor: React.FC<{node: BaseNode}> = ({node}) => {
  const properties = node.getEditableProperties();
  
  return (
    <div className="property-editor">
      {properties.map(prop => (
        <PropertyField
          key={prop.name}
          property={prop}
          onChange={(value) => node.setProperty(prop.name, value)}
        />
      ))}
    </div>
  );
};
```

### 阶段5: 测试与优化 (1周)

#### 5.1 全面测试
- **单元测试**: 每个节点的功能测试
- **集成测试**: 节点组合的功能测试
- **性能测试**: 大规模脚本的性能测试
- **用户测试**: 编辑器的可用性测试

#### 5.2 性能优化
- **执行优化**: 优化节点执行效率
- **内存优化**: 减少内存占用
- **渲染优化**: 优化编辑器渲染性能
- **加载优化**: 优化系统启动时间

## 📊 项目里程碑

### 里程碑1: 基础架构完成 (第3周)
- ✅ 新架构搭建完成
- ✅ 核心引擎重构完成
- ✅ 基础节点系统建立

### 里程碑2: 核心节点完成 (第5周)
- ✅ 50个核心基础节点实现
- ✅ 基础编辑器功能可用
- ✅ 简单脚本可以运行

### 里程碑3: 主要功能完成 (第11周)
- ✅ 200个主要节点实现
- ✅ 完整的编辑器功能
- ✅ 复杂应用可以构建

### 里程碑4: 系统完善 (第14周)
- ✅ 250个节点全部实现
- ✅ 编辑器体验优化
- ✅ 性能达到生产要求

### 里程碑5: 正式发布 (第15周)
- ✅ 全面测试通过
- ✅ 文档完善
- ✅ 系统正式上线

---

*此重构方案将分阶段实施，确保系统稳定性和开发效率。*
