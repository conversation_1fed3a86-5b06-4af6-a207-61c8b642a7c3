/**
 * 视觉脚本编辑器样式
 */

.visual-script-editor {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部工具栏样式 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background: #001529;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.editor-title {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.modified-indicator {
  color: #faad14;
  font-size: 20px;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 侧边栏样式 */
.node-panel-sider,
.property-panel-sider {
  background: white;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
}

.property-panel-sider {
  border-right: none;
  border-left: 1px solid #f0f0f0;
}

/* 画布内容区域 */
.canvas-content {
  background: #f5f5f5;
  padding: 0;
  overflow: hidden;
  position: relative;
}

/* 布局调整 */
.visual-script-editor .ant-layout {
  background: white;
}

.visual-script-editor .ant-layout-header {
  height: 64px;
  line-height: 64px;
  padding: 0 24px;
}

.visual-script-editor .ant-layout-sider {
  background: white;
}

.visual-script-editor .ant-layout-content {
  margin: 0;
  padding: 0;
  background: #f5f5f5;
}

/* 按钮样式调整 */
.editor-header .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.editor-header .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.editor-header .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 1000;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #d9d9d9;
  font-size: 12px;
  color: #666;
}

.status-indicator.modified {
  border-color: #faad14;
  background: #fffbe6;
  color: #d48806;
}

.status-indicator.saved {
  border-color: #52c41a;
  background: #f6ffed;
  color: #389e0d;
}

/* 快捷键提示 */
.shortcuts-hint {
  position: absolute;
  bottom: 16px;
  left: 16px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 11px;
  opacity: 0.8;
  pointer-events: none;
}

.shortcuts-hint .shortcut-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.shortcuts-hint .shortcut-item:last-child {
  margin-bottom: 0;
}

.shortcuts-hint .shortcut-key {
  background: rgba(255, 255, 255, 0.2);
  padding: 1px 4px;
  border-radius: 2px;
  font-family: monospace;
  margin-left: 8px;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content .ant-spin {
  margin-bottom: 12px;
}

/* 错误状态 */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #ff4d4f;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #ffccc7;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .node-panel-sider,
  .property-panel-sider {
    width: 250px !important;
    min-width: 250px !important;
    max-width: 250px !important;
  }
}

@media (max-width: 768px) {
  .editor-header {
    padding: 0 12px;
    flex-direction: column;
    height: auto;
    padding: 12px;
    gap: 12px;
  }
  
  .header-left,
  .header-right {
    width: 100%;
    justify-content: center;
  }
  
  .editor-title {
    font-size: 16px;
  }
  
  .node-panel-sider,
  .property-panel-sider {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;
  }
  
  .shortcuts-hint {
    display: none;
  }
}

@media (max-width: 480px) {
  .visual-script-editor .ant-layout {
    flex-direction: column;
  }
  
  .node-panel-sider,
  .property-panel-sider {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    height: 200px;
  }
  
  .canvas-content {
    flex: 1;
    min-height: 400px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .visual-script-editor {
    background: #141414;
  }
  
  .editor-header {
    background: #001529;
    border-bottom-color: #303030;
  }
  
  .node-panel-sider,
  .property-panel-sider {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .canvas-content {
    background: #0f1419;
  }
  
  .visual-script-editor .ant-layout {
    background: #141414;
  }
  
  .visual-script-editor .ant-layout-sider {
    background: #1f1f1f;
  }
  
  .visual-script-editor .ant-layout-content {
    background: #0f1419;
  }
  
  .status-indicator {
    background: #1f1f1f;
    border-color: #303030;
    color: #a6a6a6;
  }
  
  .status-indicator.modified {
    background: #2a2619;
    border-color: #ffc53d;
    color: #faad14;
  }
  
  .status-indicator.saved {
    background: #1f2a1f;
    border-color: #73d13d;
    color: #52c41a;
  }
  
  .error-message {
    background: #1f1f1f;
    border-color: #ff7875;
    color: #ff7875;
  }
  
  .loading-overlay {
    background: rgba(20, 20, 20, 0.8);
  }
  
  .loading-content {
    color: #a6a6a6;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .editor-header {
    border-bottom-width: 2px;
  }
  
  .node-panel-sider,
  .property-panel-sider {
    border-width: 2px;
  }
  
  .status-indicator,
  .error-message {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .editor-header .ant-btn,
  .status-indicator {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .editor-header,
  .node-panel-sider,
  .property-panel-sider {
    display: none;
  }
  
  .canvas-content {
    width: 100%;
    height: 100vh;
    background: white;
  }
  
  .status-indicator,
  .shortcuts-hint,
  .loading-overlay {
    display: none;
  }
}
