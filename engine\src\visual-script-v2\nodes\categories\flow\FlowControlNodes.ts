/**
 * 流程控制节点
 * 提供条件分支、循环等流程控制功能
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 顺序执行节点
 * 按顺序执行多个输出
 */
export class SequenceNode extends BaseNode {
  public readonly type = 'flow/sequence';
  public readonly category = NodeCategory.FLOW_CONTROL;

  protected getDefaultName(): string {
    return '顺序执行';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发顺序执行'
    });

    // 输出端口
    this.addOutputPort({
      name: 'then1',
      label: '然后1',
      type: DataType.TRIGGER,
      required: false,
      description: '第一个执行输出'
    });

    this.addOutputPort({
      name: 'then2',
      label: '然后2',
      type: DataType.TRIGGER,
      required: false,
      description: '第二个执行输出'
    });

    this.addOutputPort({
      name: 'then3',
      label: '然后3',
      type: DataType.TRIGGER,
      required: false,
      description: '第三个执行输出'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      // 按顺序触发所有输出
      this.triggerOutput(context, 'then1');
      this.triggerOutput(context, 'then2');
      this.triggerOutput(context, 'then3');
    }
  }
}

/**
 * 条件分支节点
 * 根据条件选择执行路径
 */
export class BranchNode extends BaseNode {
  public readonly type = 'flow/branch';
  public readonly category = NodeCategory.FLOW_CONTROL;

  protected getDefaultName(): string {
    return '条件分支';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发条件判断'
    });

    this.addInputPort({
      name: 'condition',
      label: '条件',
      type: DataType.BOOLEAN,
      required: true,
      defaultValue: false,
      description: '判断条件'
    });

    // 输出端口
    this.addOutputPort({
      name: 'true',
      label: '真',
      type: DataType.TRIGGER,
      required: false,
      description: '条件为真时执行'
    });

    this.addOutputPort({
      name: 'false',
      label: '假',
      type: DataType.TRIGGER,
      required: false,
      description: '条件为假时执行'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const condition = this.getInput(context, 'condition', false);
      
      if (condition) {
        this.triggerOutput(context, 'true');
      } else {
        this.triggerOutput(context, 'false');
      }
    }
  }
}

/**
 * For循环节点
 * 执行指定次数的循环
 */
export class ForLoopNode extends BaseNode {
  public readonly type = 'flow/forLoop';
  public readonly category = NodeCategory.FLOW_CONTROL;

  private currentIndex: number = 0;

  protected getDefaultName(): string {
    return 'For循环';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '开始循环'
    });

    this.addInputPort({
      name: 'startIndex',
      label: '起始索引',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '循环起始索引'
    });

    this.addInputPort({
      name: 'endIndex',
      label: '结束索引',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 10,
      description: '循环结束索引'
    });

    this.addInputPort({
      name: 'step',
      label: '步长',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '循环步长'
    });

    // 输出端口
    this.addOutputPort({
      name: 'loopBody',
      label: '循环体',
      type: DataType.TRIGGER,
      required: false,
      description: '每次循环执行'
    });

    this.addOutputPort({
      name: 'index',
      label: '当前索引',
      type: DataType.NUMBER,
      required: false,
      description: '当前循环索引'
    });

    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '循环完成时执行'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const startIndex = this.getInput(context, 'startIndex', 0);
      const endIndex = this.getInput(context, 'endIndex', 10);
      const step = this.getInput(context, 'step', 1);

      for (let i = startIndex; i < endIndex; i += step) {
        this.currentIndex = i;
        this.setOutput(context, 'index', i);
        this.triggerOutput(context, 'loopBody');
        
        // 添加小延迟避免阻塞
        await this.delay(1);
      }

      this.triggerOutput(context, 'completed');
    }
  }
}

/**
 * While循环节点
 * 根据条件执行循环
 */
export class WhileLoopNode extends BaseNode {
  public readonly type = 'flow/whileLoop';
  public readonly category = NodeCategory.FLOW_CONTROL;

  private maxIterations: number = 1000; // 防止无限循环

  protected getDefaultName(): string {
    return 'While循环';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '开始循环'
    });

    this.addInputPort({
      name: 'condition',
      label: '条件',
      type: DataType.BOOLEAN,
      required: true,
      defaultValue: false,
      description: '循环条件'
    });

    // 输出端口
    this.addOutputPort({
      name: 'loopBody',
      label: '循环体',
      type: DataType.TRIGGER,
      required: false,
      description: '每次循环执行'
    });

    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '循环完成时执行'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'maxIterations',
      label: '最大迭代次数',
      type: 'number',
      value: 1000,
      defaultValue: 1000,
      min: 1,
      max: 10000,
      description: '防止无限循环的最大迭代次数'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      this.maxIterations = this.getProperty('maxIterations');
      let iterations = 0;

      while (this.getInput(context, 'condition', false) && iterations < this.maxIterations) {
        this.triggerOutput(context, 'loopBody');
        iterations++;
        
        // 添加小延迟避免阻塞
        await this.delay(1);
      }

      if (iterations >= this.maxIterations) {
        this.log(context, 'warn', `While循环达到最大迭代次数限制: ${this.maxIterations}`);
      }

      this.triggerOutput(context, 'completed');
    }
  }
}

/**
 * 延迟节点
 * 延迟指定时间后执行
 */
export class DelayNode extends BaseNode {
  public readonly type = 'flow/delay';
  public readonly category = NodeCategory.FLOW_CONTROL;

  protected getDefaultName(): string {
    return '延迟';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '开始延迟'
    });

    this.addInputPort({
      name: 'duration',
      label: '延迟时间',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1.0,
      description: '延迟时间（秒）'
    });

    // 输出端口
    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '延迟完成后执行'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const duration = this.getInput(context, 'duration', 1.0);
      const delayMs = duration * 1000;

      this.log(context, 'info', `延迟 ${duration} 秒`);
      await this.delay(delayMs);
      
      this.triggerOutput(context, 'completed');
    }
  }
}

/**
 * 开关节点
 * 根据索引选择执行路径
 */
export class SwitchNode extends BaseNode {
  public readonly type = 'flow/switch';
  public readonly category = NodeCategory.FLOW_CONTROL;

  protected getDefaultName(): string {
    return '开关选择';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发开关选择'
    });

    this.addInputPort({
      name: 'selection',
      label: '选择索引',
      type: DataType.NUMBER,
      required: true,
      defaultValue: 0,
      description: '选择的输出索引'
    });

    // 输出端口
    this.addOutputPort({
      name: 'option0',
      label: '选项0',
      type: DataType.TRIGGER,
      required: false,
      description: '索引为0时执行'
    });

    this.addOutputPort({
      name: 'option1',
      label: '选项1',
      type: DataType.TRIGGER,
      required: false,
      description: '索引为1时执行'
    });

    this.addOutputPort({
      name: 'option2',
      label: '选项2',
      type: DataType.TRIGGER,
      required: false,
      description: '索引为2时执行'
    });

    this.addOutputPort({
      name: 'default',
      label: '默认',
      type: DataType.TRIGGER,
      required: false,
      description: '没有匹配选项时执行'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'execute')) {
      const selection = Math.floor(this.getInput(context, 'selection', 0));

      switch (selection) {
        case 0:
          this.triggerOutput(context, 'option0');
          break;
        case 1:
          this.triggerOutput(context, 'option1');
          break;
        case 2:
          this.triggerOutput(context, 'option2');
          break;
        default:
          this.triggerOutput(context, 'default');
          break;
      }
    }
  }
}

/**
 * 门控制节点
 * 控制信号的通过
 */
export class GateNode extends BaseNode {
  public readonly type = 'flow/gate';
  public readonly category = NodeCategory.FLOW_CONTROL;

  private isOpen: boolean = false;

  protected getDefaultName(): string {
    return '门控制';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'enter',
      label: '进入',
      type: DataType.TRIGGER,
      required: false,
      description: '尝试通过门的信号'
    });

    this.addInputPort({
      name: 'open',
      label: '打开',
      type: DataType.TRIGGER,
      required: false,
      description: '打开门'
    });

    this.addInputPort({
      name: 'close',
      label: '关闭',
      type: DataType.TRIGGER,
      required: false,
      description: '关闭门'
    });

    this.addInputPort({
      name: 'toggle',
      label: '切换',
      type: DataType.TRIGGER,
      required: false,
      description: '切换门的状态'
    });

    // 输出端口
    this.addOutputPort({
      name: 'exit',
      label: '退出',
      type: DataType.TRIGGER,
      required: false,
      description: '信号通过门后的输出'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'startOpen',
      label: '初始打开',
      type: 'boolean',
      value: false,
      defaultValue: false,
      description: '门的初始状态是否为打开'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    // 处理门控制
    if (this.hasInput(context, 'open')) {
      this.isOpen = true;
      this.log(context, 'info', '门已打开');
    }

    if (this.hasInput(context, 'close')) {
      this.isOpen = false;
      this.log(context, 'info', '门已关闭');
    }

    if (this.hasInput(context, 'toggle')) {
      this.isOpen = !this.isOpen;
      this.log(context, 'info', `门已${this.isOpen ? '打开' : '关闭'}`);
    }

    // 处理信号通过
    if (this.hasInput(context, 'enter')) {
      if (this.isOpen) {
        this.triggerOutput(context, 'exit');
      } else {
        this.log(context, 'info', '门已关闭，信号被阻止');
      }
    }
  }
}

/**
 * 执行一次节点
 * 只执行一次，后续调用被忽略
 */
export class DoOnceNode extends BaseNode {
  public readonly type = 'flow/doOnce';
  public readonly category = NodeCategory.FLOW_CONTROL;

  private hasExecuted: boolean = false;

  protected getDefaultName(): string {
    return '执行一次';
  }

  protected initializePorts(): void {
    // 输入端口
    this.addInputPort({
      name: 'execute',
      label: '执行',
      type: DataType.TRIGGER,
      required: true,
      description: '触发执行'
    });

    this.addInputPort({
      name: 'reset',
      label: '重置',
      type: DataType.TRIGGER,
      required: false,
      description: '重置执行状态'
    });

    // 输出端口
    this.addOutputPort({
      name: 'completed',
      label: '完成',
      type: DataType.TRIGGER,
      required: false,
      description: '第一次执行时触发'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    if (this.hasInput(context, 'reset')) {
      this.hasExecuted = false;
      this.log(context, 'info', '执行状态已重置');
    }

    if (this.hasInput(context, 'execute')) {
      if (!this.hasExecuted) {
        this.hasExecuted = true;
        this.triggerOutput(context, 'completed');
        this.log(context, 'info', '首次执行完成');
      } else {
        this.log(context, 'info', '已执行过，忽略此次调用');
      }
    }
  }
}
