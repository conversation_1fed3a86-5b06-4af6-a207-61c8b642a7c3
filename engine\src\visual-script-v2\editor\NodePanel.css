/**
 * 节点面板样式
 */

.node-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
  border-right: 1px solid #d9d9d9;
  overflow: hidden;
}

/* 搜索区域 */
.search-section {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

/* 分类区域 */
.category-section {
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

.category-section .ant-tabs {
  margin: 0;
}

.category-section .ant-tabs-tab {
  margin: 0 !important;
  padding: 8px 12px !important;
}

.category-section .ant-tabs-content-holder {
  display: none;
}

/* 节点列表区域 */
.nodes-section {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.nodes-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 节点项样式 */
.node-item {
  transition: all 0.2s ease;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.node-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.node-item:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.node-item.favorite {
  border-width: 2px;
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.node-item-content {
  width: 100%;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.node-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-name {
  font-weight: 500;
  color: #262626;
  flex: 1;
  font-size: 13px;
}

.node-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.node-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.node-tags .ant-badge {
  font-size: 10px;
}

.node-tags .ant-badge-count {
  height: 16px;
  line-height: 14px;
  padding: 0 4px;
  font-size: 10px;
  border-radius: 8px;
}

/* 统计信息区域 */
.stats-section {
  padding: 8px 12px;
  border-top: 1px solid #f0f0f0;
  background: white;
  font-size: 11px;
  color: #8c8c8c;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}

/* 拖拽状态 */
.node-item[draggable="true"] {
  cursor: grab;
}

.node-item[draggable="true"]:active {
  cursor: grabbing;
}

/* 空状态 */
.nodes-section .ant-empty {
  margin: 40px 0;
}

/* 滚动条样式 */
.nodes-section::-webkit-scrollbar {
  width: 6px;
}

.nodes-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.nodes-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.nodes-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-panel {
    width: 100% !important;
  }
  
  .search-section {
    padding: 8px;
  }
  
  .nodes-section {
    padding: 4px;
  }
  
  .nodes-list {
    gap: 4px;
  }
  
  .node-item {
    margin-bottom: 0;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .node-panel {
    background: #141414;
    border-right-color: #303030;
  }
  
  .search-section,
  .category-section,
  .stats-section {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .node-item {
    background: #262626;
    border-color: #434343;
  }
  
  .node-item:hover {
    background: #2f2f2f;
  }
  
  .node-item.favorite {
    background: linear-gradient(135deg, #2a2a1a 0%, #262626 100%);
  }
  
  .node-name {
    color: #f0f0f0;
  }
  
  .node-description {
    color: #a6a6a6;
  }
  
  .stats-section {
    color: #a6a6a6;
  }
}

/* 动画效果 */
@keyframes nodeAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.node-item {
  animation: nodeAppear 0.3s ease-out;
}

/* 拖拽提示 */
.node-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.1);
  border: 2px dashed #1890ff;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.node-item:active::after {
  opacity: 1;
}

/* 收藏按钮样式 */
.node-item .ant-card-actions {
  border-top: 1px solid #f0f0f0;
  background: rgba(0, 0, 0, 0.02);
}

.node-item .ant-card-actions > li {
  margin: 0;
  padding: 4px 0;
}

.node-item .ant-card-actions > li > span {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: color 0.2s ease;
}

.node-item .ant-card-actions > li > span:hover {
  color: #1890ff;
}

/* 分类标签徽章样式 */
.category-section .ant-badge {
  margin-left: 4px;
}

.category-section .ant-badge-count {
  background: #f0f0f0;
  color: #666;
  border: none;
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  min-width: 16px;
  padding: 0 4px;
}

.category-section .ant-tabs-tab-active .ant-badge-count {
  background: #1890ff;
  color: white;
}

/* 搜索框样式优化 */
.search-section .ant-input-affix-wrapper {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.search-section .ant-input-affix-wrapper:focus,
.search-section .ant-input-affix-wrapper-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
