/**
 * 逻辑运算节点
 * 提供布尔逻辑和比较运算功能
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 逻辑与节点
 */
export class AndNode extends BaseNode {
  public readonly type = 'logic/and';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '逻辑与';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '条件A',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: false,
      description: '第一个布尔值'
    });

    this.addInputPort({
      name: 'b',
      label: '条件B',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: false,
      description: '第二个布尔值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: '逻辑与运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', false);
    const b = this.getInput(context, 'b', false);
    const result = Boolean(a) && Boolean(b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 逻辑或节点
 */
export class OrNode extends BaseNode {
  public readonly type = 'logic/or';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '逻辑或';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '条件A',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: false,
      description: '第一个布尔值'
    });

    this.addInputPort({
      name: 'b',
      label: '条件B',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: false,
      description: '第二个布尔值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: '逻辑或运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', false);
    const b = this.getInput(context, 'b', false);
    const result = Boolean(a) || Boolean(b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 逻辑非节点
 */
export class NotNode extends BaseNode {
  public readonly type = 'logic/not';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '逻辑非';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'value',
      label: '输入值',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: false,
      description: '要取反的布尔值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: '逻辑非运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInput(context, 'value', false);
    const result = !Boolean(value);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 等于比较节点
 */
export class EqualNode extends BaseNode {
  public readonly type = 'logic/equal';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '等于';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '值A',
      type: DataType.ANY,
      required: false,
      description: '第一个比较值'
    });

    this.addInputPort({
      name: 'b',
      label: '值B',
      type: DataType.ANY,
      required: false,
      description: '第二个比较值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: '比较结果'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'strictMode',
      label: '严格模式',
      type: 'boolean',
      value: false,
      defaultValue: false,
      description: '是否使用严格相等比较（===）'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a');
    const b = this.getInput(context, 'b');
    const strictMode = this.getProperty('strictMode');
    
    const result = strictMode ? (a === b) : (a == b);
    this.setOutput(context, 'result', result);
  }
}

/**
 * 不等于比较节点
 */
export class NotEqualNode extends BaseNode {
  public readonly type = 'logic/notEqual';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '不等于';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '值A',
      type: DataType.ANY,
      required: false,
      description: '第一个比较值'
    });

    this.addInputPort({
      name: 'b',
      label: '值B',
      type: DataType.ANY,
      required: false,
      description: '第二个比较值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: '比较结果'
    });
  }

  protected initializeProperties(): void {
    super.initializeProperties();

    this.addProperty({
      name: 'strictMode',
      label: '严格模式',
      type: 'boolean',
      value: false,
      defaultValue: false,
      description: '是否使用严格不等比较（!==）'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a');
    const b = this.getInput(context, 'b');
    const strictMode = this.getProperty('strictMode');
    
    const result = strictMode ? (a !== b) : (a != b);
    this.setOutput(context, 'result', result);
  }
}

/**
 * 大于比较节点
 */
export class GreaterNode extends BaseNode {
  public readonly type = 'logic/greater';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '大于';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个数值'
    });

    this.addInputPort({
      name: 'b',
      label: '值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: 'A > B 的比较结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = Number(a) > Number(b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 小于比较节点
 */
export class LessNode extends BaseNode {
  public readonly type = 'logic/less';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '小于';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个数值'
    });

    this.addInputPort({
      name: 'b',
      label: '值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: 'A < B 的比较结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = Number(a) < Number(b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 大于等于比较节点
 */
export class GreaterEqualNode extends BaseNode {
  public readonly type = 'logic/greaterEqual';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '大于等于';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个数值'
    });

    this.addInputPort({
      name: 'b',
      label: '值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: 'A >= B 的比较结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = Number(a) >= Number(b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 小于等于比较节点
 */
export class LessEqualNode extends BaseNode {
  public readonly type = 'logic/lessEqual';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '小于等于';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'a',
      label: '值A',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第一个数值'
    });

    this.addInputPort({
      name: 'b',
      label: '值B',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个数值'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.BOOLEAN,
      required: false,
      description: 'A <= B 的比较结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const a = this.getInput(context, 'a', 0);
    const b = this.getInput(context, 'b', 0);
    const result = Number(a) <= Number(b);
    
    this.setOutput(context, 'result', result);
  }
}

/**
 * 有效性检查节点
 */
export class IsValidNode extends BaseNode {
  public readonly type = 'logic/isValid';
  public readonly category = NodeCategory.LOGIC;

  protected getDefaultName(): string {
    return '有效性检查';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'value',
      label: '输入值',
      type: DataType.ANY,
      required: false,
      description: '要检查的值'
    });

    this.addOutputPort({
      name: 'isValid',
      label: '有效',
      type: DataType.BOOLEAN,
      required: false,
      description: '值是否有效（非null、undefined、NaN）'
    });

    this.addOutputPort({
      name: 'isNull',
      label: '为空',
      type: DataType.BOOLEAN,
      required: false,
      description: '值是否为null或undefined'
    });

    this.addOutputPort({
      name: 'isNumber',
      label: '是数字',
      type: DataType.BOOLEAN,
      required: false,
      description: '值是否为有效数字'
    });

    this.addOutputPort({
      name: 'isString',
      label: '是字符串',
      type: DataType.BOOLEAN,
      required: false,
      description: '值是否为字符串'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInput(context, 'value');
    
    const isNull = value === null || value === undefined;
    const isNumber = typeof value === 'number' && !isNaN(value);
    const isString = typeof value === 'string';
    const isValid = !isNull && (isNumber ? !isNaN(value) : true);
    
    this.setOutput(context, 'isValid', isValid);
    this.setOutput(context, 'isNull', isNull);
    this.setOutput(context, 'isNumber', isNumber);
    this.setOutput(context, 'isString', isString);
  }
}
